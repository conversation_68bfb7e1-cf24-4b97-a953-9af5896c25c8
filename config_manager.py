"""
Configuration Manager for Kotak Neo Trading Bot
Handles secure loading and management of configuration settings
"""

import configparser
import os
import logging
from typing import Dict, Any, Optional

class ConfigManager:
    """Manages configuration settings for the trading bot"""
    
    def __init__(self, config_file: str = 'config.ini'):
        """
        Initialize the configuration manager
        
        Args:
            config_file: Path to the configuration file
        """
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self._load_config()
    
    def _load_config(self) -> None:
        """Load configuration from file"""
        try:
            if not os.path.exists(self.config_file):
                raise FileNotFoundError(f"Configuration file {self.config_file} not found")
            
            self.config.read(self.config_file)
            logging.info(f"Configuration loaded from {self.config_file}")
            
        except Exception as e:
            logging.error(f"Failed to load configuration: {e}")
            raise
    
    def get(self, key: str, section: str = 'DEFAULT', fallback: Any = None) -> str:
        """
        Get a configuration value
        
        Args:
            key: Configuration key
            section: Configuration section
            fallback: Default value if key not found
            
        Returns:
            Configuration value
        """
        try:
            return self.config.get(section, key, fallback=fallback)
        except (configparser.NoSectionError, configparser.NoOptionError):
            if fallback is not None:
                return fallback
            raise
    
    def get_int(self, key: str, section: str = 'DEFAULT', fallback: int = None) -> int:
        """Get an integer configuration value"""
        try:
            return self.config.getint(section, key, fallback=fallback)
        except (configparser.NoSectionError, configparser.NoOptionError):
            if fallback is not None:
                return fallback
            raise
    
    def get_float(self, key: str, section: str = 'DEFAULT', fallback: float = None) -> float:
        """Get a float configuration value"""
        try:
            return self.config.getfloat(section, key, fallback=fallback)
        except (configparser.NoSectionError, configparser.NoOptionError):
            if fallback is not None:
                return fallback
            raise
    
    def get_boolean(self, key: str, section: str = 'DEFAULT', fallback: bool = None) -> bool:
        """Get a boolean configuration value"""
        try:
            return self.config.getboolean(section, key, fallback=fallback)
        except (configparser.NoSectionError, configparser.NoOptionError):
            if fallback is not None:
                return fallback
            raise
    
    def get_credentials(self) -> Dict[str, str]:
        """
        Get API credentials
        
        Returns:
            Dictionary containing API credentials
        """
        return {
            'consumer_key': self.get('CONSUMER_KEY'),
            'consumer_secret': self.get('CONSUMER_SECRET'),
            'ucc': self.get('UCC'),
            'mobile_number': self.get('MOBILE_NUMBER'),
            'password': self.get('PASSWORD'),
            'mpin': self.get('MPIN')
        }
    
    def get_trading_settings(self) -> Dict[str, Any]:
        """
        Get trading configuration settings
        
        Returns:
            Dictionary containing trading settings
        """
        return {
            'default_quantity': self.get_int('DEFAULT_QUANTITY', fallback=1),
            'stop_loss_percentage': self.get_float('STOP_LOSS_PERCENTAGE', fallback=2.0),
            'target_profit_percentage': self.get_float('TARGET_PROFIT_PERCENTAGE', fallback=3.0),
            'max_daily_loss': self.get_float('MAX_DAILY_LOSS', fallback=5000),
            'max_position_size': self.get_float('MAX_POSITION_SIZE', fallback=10000)
        }
    
    def validate_credentials(self) -> bool:
        """
        Validate that all required credentials are present
        
        Returns:
            True if all credentials are valid, False otherwise
        """
        required_keys = ['CONSUMER_KEY', 'CONSUMER_SECRET', 'UCC', 'MOBILE_NUMBER', 'PASSWORD', 'MPIN']
        
        for key in required_keys:
            value = self.get(key, fallback='')
            if not value or value == 'YOUR_KEY_HERE' or value.startswith('YOUR_'):
                logging.error(f"Invalid or missing credential: {key}")
                return False
        
        return True

# Global configuration instance
_config_manager = None

def get_config() -> ConfigManager:
    """
    Get the global configuration manager instance
    
    Returns:
        ConfigManager instance
    """
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager

def reload_config() -> ConfigManager:
    """
    Reload the configuration from file
    
    Returns:
        Reloaded ConfigManager instance
    """
    global _config_manager
    _config_manager = ConfigManager()
    return _config_manager
