#!/usr/bin/env python3
"""
Example Usage Script for Kotak Neo Trading Bot
Demonstrates basic functionality and API usage
"""

import time
import queue
import logging
from datetime import datetime, timedelta

# Import our modules
from core_engine import CoreEngine
from data_handler import DataHandler
from order_manager import OrderManager, Order, OrderType, OrderSide
from trade_scheduler import TradeScheduler

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def example_basic_connection():
    """Example: Basic API connection"""
    print("\n📡 Example: Basic API Connection")
    print("-" * 40)
    
    try:
        # Initialize core engine
        core_engine = CoreEngine()
        
        # Attempt connection
        print("Connecting to Kotak Neo API...")
        success = core_engine.connect()
        
        if success:
            print("✅ Connected successfully!")
            
            # Get connection status
            status = core_engine.get_connection_status()
            print(f"Connection Status: {status}")
            
            # Disconnect
            core_engine.disconnect()
            print("✅ Disconnected successfully!")
            
        else:
            print("❌ Connection failed!")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def example_instrument_search():
    """Example: Search for instruments"""
    print("\n🔍 Example: Instrument Search")
    print("-" * 40)
    
    try:
        core_engine = CoreEngine()
        
        if core_engine.connect():
            # Search for instruments
            symbols = ['RELIANCE', 'TCS', 'INFY']
            
            for symbol in symbols:
                instrument = core_engine.get_instrument_details(symbol)
                if instrument:
                    print(f"✅ Found {symbol}: Token={instrument.get('pToken', 'N/A')}")
                else:
                    print(f"❌ {symbol} not found")
            
            # Search with partial match
            search_results = core_engine.search_instruments('HDFC', limit=5)
            print(f"\n🔍 Search results for 'HDFC': {len(search_results)} found")
            
            for result in search_results[:3]:  # Show first 3
                symbol = result.get('pTrdSymbol', result.get('symbol', 'Unknown'))
                print(f"  - {symbol}")
            
            core_engine.disconnect()
            
    except Exception as e:
        print(f"❌ Error: {e}")

def example_data_subscription():
    """Example: Real-time data subscription"""
    print("\n📊 Example: Real-time Data Subscription")
    print("-" * 40)
    
    try:
        # Initialize components
        core_engine = CoreEngine()
        data_queue = queue.Queue()
        data_handler = DataHandler(core_engine, data_queue)
        
        if core_engine.connect():
            print("✅ Connected to API")
            
            # Start WebSocket
            if data_handler.start_websocket():
                print("✅ WebSocket started")
                
                # Subscribe to symbols
                symbols = ['RELIANCE', 'TCS']
                if data_handler.subscribe_to_symbols(symbols):
                    print(f"✅ Subscribed to {symbols}")
                    
                    # Listen for data for 10 seconds
                    print("📡 Listening for data (10 seconds)...")
                    start_time = time.time()
                    
                    while time.time() - start_time < 10:
                        try:
                            # Check for new data
                            if not data_queue.empty():
                                data = data_queue.get_nowait()
                                print(f"📊 Received: {data.get('type', 'Unknown')}")
                            
                            time.sleep(1)
                            
                        except queue.Empty:
                            continue
                    
                    print("✅ Data subscription test completed")
                    
                else:
                    print("❌ Failed to subscribe to symbols")
                
                # Stop WebSocket
                data_handler.stop_websocket()
                print("✅ WebSocket stopped")
                
            else:
                print("❌ Failed to start WebSocket")
            
            core_engine.disconnect()
            
    except Exception as e:
        print(f"❌ Error: {e}")

def example_order_placement():
    """Example: Order placement (DEMO - does not place real orders)"""
    print("\n💹 Example: Order Placement (DEMO)")
    print("-" * 40)
    
    try:
        # Initialize components
        core_engine = CoreEngine()
        data_queue = queue.Queue()
        order_queue = queue.Queue()
        data_handler = DataHandler(core_engine, data_queue)
        order_manager = OrderManager(core_engine, data_handler, order_queue)
        
        print("⚠️  This is a DEMO - no real orders will be placed")
        
        # Create sample orders
        orders = [
            Order(
                symbol="RELIANCE",
                side=OrderSide.BUY,
                quantity=1,
                order_type=OrderType.MARKET
            ),
            Order(
                symbol="TCS",
                side=OrderSide.BUY,
                quantity=1,
                order_type=OrderType.LIMIT,
                price=3500.0
            )
        ]
        
        for i, order in enumerate(orders, 1):
            print(f"\n📝 Sample Order {i}:")
            print(f"  Symbol: {order.symbol}")
            print(f"  Side: {order.side.value}")
            print(f"  Quantity: {order.quantity}")
            print(f"  Type: {order.order_type.value}")
            if order.price > 0:
                print(f"  Price: ₹{order.price}")
            
            # Validate order (without placing)
            is_valid = order_manager._validate_order(order)
            print(f"  Validation: {'✅ Valid' if is_valid else '❌ Invalid'}")
        
        # Show order manager status
        status = order_manager.get_order_status()
        print(f"\n📊 Order Manager Status: {status}")
        
        # Stop monitoring
        order_manager.stop_monitoring()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def example_trade_scheduling():
    """Example: Trade scheduling"""
    print("\n⏰ Example: Trade Scheduling")
    print("-" * 40)
    
    try:
        # Initialize components
        core_engine = CoreEngine()
        data_queue = queue.Queue()
        order_queue = queue.Queue()
        scheduler_queue = queue.Queue()
        
        data_handler = DataHandler(core_engine, data_queue)
        order_manager = OrderManager(core_engine, data_handler, order_queue)
        trade_scheduler = TradeScheduler(order_manager, data_handler, scheduler_queue)
        
        print("✅ Trade scheduler initialized")
        
        # Create a sample scheduled order (5 seconds from now)
        future_time = datetime.now() + timedelta(seconds=5)
        
        sample_order = Order(
            symbol="DEMO",
            side=OrderSide.BUY,
            quantity=1,
            order_type=OrderType.MARKET
        )
        
        # Schedule the order (DEMO - won't actually execute)
        print(f"📅 Scheduling demo order for {future_time.strftime('%H:%M:%S')}")
        
        # Note: In real usage, this would schedule an actual order
        print("⚠️  This is a DEMO - no real order will be scheduled")
        
        # Show scheduler status
        status = trade_scheduler.get_scheduler_status()
        print(f"📊 Scheduler Status: {status}")
        
        # Show upcoming runs
        upcoming = trade_scheduler.get_next_runs(limit=5)
        print(f"📅 Upcoming tasks: {len(upcoming)}")
        
        # Shutdown scheduler
        trade_scheduler.shutdown()
        order_manager.stop_monitoring()
        
        print("✅ Trade scheduling example completed")
        
    except Exception as e:
        print(f"❌ Error: {e}")

def example_portfolio_monitoring():
    """Example: Portfolio monitoring"""
    print("\n💼 Example: Portfolio Monitoring")
    print("-" * 40)
    
    try:
        # Initialize components
        core_engine = CoreEngine()
        data_queue = queue.Queue()
        order_queue = queue.Queue()
        
        data_handler = DataHandler(core_engine, data_queue)
        order_manager = OrderManager(core_engine, data_handler, order_queue)
        
        print("✅ Portfolio monitoring initialized")
        
        # Get current positions (will be empty in demo)
        positions = order_manager.get_positions()
        print(f"📊 Current positions: {len(positions)}")
        
        # Get orders (will be empty in demo)
        orders = order_manager.get_orders()
        print(f"📋 Current orders: {len(orders)}")
        
        # Get daily P&L
        daily_pnl = order_manager.get_daily_pnl()
        print(f"💰 Daily P&L: ₹{daily_pnl:.2f}")
        
        # Show order manager status
        status = order_manager.get_order_status()
        print(f"📊 Order Manager Status:")
        for key, value in status.items():
            print(f"  {key}: {value}")
        
        order_manager.stop_monitoring()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Main example runner"""
    print("🚀 Kotak Neo Trading Bot - Example Usage")
    print("=" * 60)
    
    print("\n⚠️  IMPORTANT DISCLAIMERS:")
    print("1. These are demonstration examples")
    print("2. No real trades will be placed")
    print("3. Ensure your config.ini is properly configured")
    print("4. Test in UAT environment before live trading")
    
    # Run examples
    examples = [
        ("Basic Connection", example_basic_connection),
        ("Instrument Search", example_instrument_search),
        ("Data Subscription", example_data_subscription),
        ("Order Placement (DEMO)", example_order_placement),
        ("Trade Scheduling", example_trade_scheduling),
        ("Portfolio Monitoring", example_portfolio_monitoring)
    ]
    
    for name, example_func in examples:
        try:
            example_func()
            time.sleep(2)  # Brief pause between examples
        except KeyboardInterrupt:
            print("\n⏹️  Examples stopped by user")
            break
        except Exception as e:
            print(f"\n❌ Error in {name}: {e}")
            continue
    
    print("\n✅ All examples completed!")
    print("\n📚 Next Steps:")
    print("1. Review the code in each module")
    print("2. Configure your credentials in config.ini")
    print("3. Run the full application with: python run_bot.py")
    print("4. Start with UAT environment for testing")

if __name__ == "__main__":
    main()
