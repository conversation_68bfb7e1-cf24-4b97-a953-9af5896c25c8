"""
Kotak Neo Trading Bot - Main Streamlit Application
Professional trading interface with real-time data and automated trading capabilities
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
import queue
import threading
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List

# Import our modules
from core_engine import CoreEngine
from data_handler import DataHandler
from order_manager import OrderManager, Order, OrderType, OrderSide, OrderStatus
from trade_scheduler import TradeScheduler, ScheduleType
from config_manager import get_config

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Page configuration
st.set_page_config(
    page_title="Kotak Neo Trading Bot",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .status-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
        margin: 0.5rem 0;
    }
    .success-card {
        background-color: #d4edda;
        border-left-color: #28a745;
    }
    .warning-card {
        background-color: #fff3cd;
        border-left-color: #ffc107;
    }
    .error-card {
        background-color: #f8d7da;
        border-left-color: #dc3545;
    }
    .metric-card {
        background-color: white;
        padding: 1rem;
        border-radius: 0.5rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        text-align: center;
    }
</style>
""", unsafe_allow_html=True)

class TradingBotApp:
    """Main trading bot application class"""
    
    def __init__(self):
        """Initialize the trading bot application"""
        self.initialize_session_state()
        self.setup_components()
    
    def initialize_session_state(self):
        """Initialize Streamlit session state variables"""
        if 'initialized' not in st.session_state:
            st.session_state.initialized = False
            st.session_state.core_engine = None
            st.session_state.data_handler = None
            st.session_state.order_manager = None
            st.session_state.trade_scheduler = None
            st.session_state.data_queue = queue.Queue()
            st.session_state.order_queue = queue.Queue()
            st.session_state.scheduler_queue = queue.Queue()
            st.session_state.connection_status = False
            st.session_state.market_data = {}
            st.session_state.orders = []
            st.session_state.positions = []
            st.session_state.watchlist = ['RELIANCE', 'TCS', 'INFY', 'HDFCBANK', 'ICICIBANK']
            st.session_state.selected_symbols = []
    
    def setup_components(self):
        """Setup and initialize all components"""
        if not st.session_state.initialized:
            try:
                # Initialize core engine
                st.session_state.core_engine = CoreEngine()
                
                # Initialize data handler
                st.session_state.data_handler = DataHandler(
                    st.session_state.core_engine,
                    st.session_state.data_queue
                )
                
                # Initialize order manager
                st.session_state.order_manager = OrderManager(
                    st.session_state.core_engine,
                    st.session_state.data_handler,
                    st.session_state.order_queue
                )
                
                # Initialize trade scheduler
                st.session_state.trade_scheduler = TradeScheduler(
                    st.session_state.order_manager,
                    st.session_state.data_handler,
                    st.session_state.scheduler_queue
                )
                
                st.session_state.initialized = True
                st.success("✅ Trading bot components initialized successfully!")
                
            except Exception as e:
                st.error(f"❌ Failed to initialize components: {e}")
                logging.error(f"Component initialization error: {e}")
    
    def render_header(self):
        """Render the main header"""
        st.markdown('<h1 class="main-header">📈 Kotak Neo Trading Bot</h1>', unsafe_allow_html=True)
        
        # Connection status
        if st.session_state.core_engine:
            status = st.session_state.core_engine.get_connection_status()
            if status['is_connected']:
                st.markdown('<div class="status-card success-card">🟢 Connected to Kotak Neo API</div>', 
                          unsafe_allow_html=True)
            else:
                st.markdown('<div class="status-card error-card">🔴 Disconnected from Kotak Neo API</div>', 
                          unsafe_allow_html=True)
    
    def render_sidebar(self):
        """Render the sidebar with navigation and controls"""
        st.sidebar.title("🎛️ Control Panel")
        
        # Navigation
        page = st.sidebar.selectbox(
            "Navigate to:",
            ["Dashboard", "Trading", "Portfolio", "Scheduler", "Settings", "Logs"]
        )
        
        st.sidebar.markdown("---")
        
        # Connection controls
        st.sidebar.subheader("🔌 Connection")
        
        col1, col2 = st.sidebar.columns(2)
        
        with col1:
            if st.button("Connect", key="connect_btn"):
                self.connect_to_api()
        
        with col2:
            if st.button("Disconnect", key="disconnect_btn"):
                self.disconnect_from_api()
        
        # WebSocket controls
        st.sidebar.subheader("📡 Data Feed")
        
        col1, col2 = st.sidebar.columns(2)
        
        with col1:
            if st.button("Start Feed", key="start_feed_btn"):
                self.start_data_feed()
        
        with col2:
            if st.button("Stop Feed", key="stop_feed_btn"):
                self.stop_data_feed()
        
        st.sidebar.markdown("---")
        
        # Quick stats
        if st.session_state.order_manager:
            status = st.session_state.order_manager.get_order_status()
            st.sidebar.metric("Total Orders", status['total_orders'])
            st.sidebar.metric("Open Orders", status['open_orders'])
            st.sidebar.metric("Daily P&L", f"₹{status['daily_pnl']:.2f}")
        
        return page
    
    def connect_to_api(self):
        """Connect to Kotak Neo API"""
        try:
            if st.session_state.core_engine:
                with st.spinner("Connecting to Kotak Neo API..."):
                    success = st.session_state.core_engine.connect()
                    if success:
                        st.success("✅ Connected successfully!")
                        st.session_state.connection_status = True
                    else:
                        st.error("❌ Connection failed!")
        except Exception as e:
            st.error(f"❌ Connection error: {e}")
    
    def disconnect_from_api(self):
        """Disconnect from API"""
        try:
            if st.session_state.core_engine:
                st.session_state.core_engine.disconnect()
                if st.session_state.data_handler:
                    st.session_state.data_handler.stop_websocket()
                st.session_state.connection_status = False
                st.success("✅ Disconnected successfully!")
        except Exception as e:
            st.error(f"❌ Disconnect error: {e}")
    
    def start_data_feed(self):
        """Start WebSocket data feed"""
        try:
            if st.session_state.data_handler:
                success = st.session_state.data_handler.start_websocket()
                if success:
                    st.success("✅ Data feed started!")
                else:
                    st.error("❌ Failed to start data feed!")
        except Exception as e:
            st.error(f"❌ Data feed error: {e}")
    
    def stop_data_feed(self):
        """Stop WebSocket data feed"""
        try:
            if st.session_state.data_handler:
                st.session_state.data_handler.stop_websocket()
                st.success("✅ Data feed stopped!")
        except Exception as e:
            st.error(f"❌ Error stopping data feed: {e}")
    
    def render_dashboard(self):
        """Render the main dashboard"""
        st.header("📊 Dashboard")
        
        # Market overview
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.markdown('<div class="metric-card"><h3>Market Status</h3><p>Open</p></div>', 
                       unsafe_allow_html=True)
        
        with col2:
            if st.session_state.order_manager:
                pnl = st.session_state.order_manager.get_daily_pnl()
                color = "green" if pnl >= 0 else "red"
                st.markdown(f'<div class="metric-card"><h3>Daily P&L</h3><p style="color:{color}">₹{pnl:.2f}</p></div>', 
                           unsafe_allow_html=True)
        
        with col3:
            if st.session_state.order_manager:
                positions = len(st.session_state.order_manager.get_positions())
                st.markdown(f'<div class="metric-card"><h3>Positions</h3><p>{positions}</p></div>', 
                           unsafe_allow_html=True)
        
        with col4:
            if st.session_state.data_handler:
                status = st.session_state.data_handler.get_data_status()
                subscribed = status['subscribed_tokens']
                st.markdown(f'<div class="metric-card"><h3>Subscriptions</h3><p>{subscribed}</p></div>', 
                           unsafe_allow_html=True)
        
        st.markdown("---")
        
        # Watchlist
        st.subheader("👀 Watchlist")
        
        # Symbol search and add
        col1, col2 = st.columns([3, 1])
        
        with col1:
            new_symbol = st.text_input("Add symbol to watchlist:", placeholder="e.g., RELIANCE")
        
        with col2:
            if st.button("Add", key="add_symbol_btn"):
                if new_symbol and new_symbol.upper() not in st.session_state.watchlist:
                    st.session_state.watchlist.append(new_symbol.upper())
                    st.success(f"Added {new_symbol.upper()} to watchlist")
        
        # Display watchlist
        if st.session_state.watchlist:
            watchlist_data = []
            for symbol in st.session_state.watchlist:
                # Get latest price data
                price_data = None
                if st.session_state.data_handler:
                    price_data = st.session_state.data_handler.get_latest_price(symbol)
                
                if price_data:
                    watchlist_data.append({
                        'Symbol': symbol,
                        'LTP': f"₹{price_data['ltp']:.2f}",
                        'Change': f"{price_data['change']:.2f}",
                        'Change %': f"{price_data['change_percent']:.2f}%",
                        'Volume': price_data['volume']
                    })
                else:
                    watchlist_data.append({
                        'Symbol': symbol,
                        'LTP': 'N/A',
                        'Change': 'N/A',
                        'Change %': 'N/A',
                        'Volume': 'N/A'
                    })
            
            df = pd.DataFrame(watchlist_data)
            st.dataframe(df, use_container_width=True)
        
        # Subscribe to watchlist
        if st.button("Subscribe to Watchlist Data", key="subscribe_watchlist_btn"):
            if st.session_state.data_handler and st.session_state.watchlist:
                success = st.session_state.data_handler.subscribe_to_symbols(st.session_state.watchlist)
                if success:
                    st.success("✅ Subscribed to watchlist data!")
                else:
                    st.error("❌ Failed to subscribe to data!")
    
    def render_trading_interface(self):
        """Render the trading interface"""
        st.header("💹 Trading")
        
        # Order placement form
        st.subheader("📝 Place Order")
        
        col1, col2 = st.columns(2)
        
        with col1:
            symbol = st.text_input("Symbol:", placeholder="e.g., RELIANCE")
            side = st.selectbox("Side:", ["BUY", "SELL"])
            quantity = st.number_input("Quantity:", min_value=1, value=1)
            order_type = st.selectbox("Order Type:", ["MARKET", "LIMIT", "STOP_LOSS"])
        
        with col2:
            price = st.number_input("Price:", min_value=0.0, value=0.0, step=0.05)
            trigger_price = st.number_input("Trigger Price:", min_value=0.0, value=0.0, step=0.05)
            product = st.selectbox("Product:", ["CNC", "MIS", "NRML"])
            exchange = st.selectbox("Exchange:", ["nse_cm", "nse_fo", "mcx_fo"])
        
        # Place order button
        if st.button("Place Order", key="place_order_btn"):
            self.place_order(symbol, side, quantity, order_type, price, trigger_price, product, exchange)
        
        st.markdown("---")
        
        # Bracket order section
        st.subheader("🎯 Bracket Order")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            bracket_symbol = st.text_input("Symbol:", placeholder="e.g., TCS", key="bracket_symbol")
            bracket_side = st.selectbox("Side:", ["BUY", "SELL"], key="bracket_side")
            bracket_quantity = st.number_input("Quantity:", min_value=1, value=1, key="bracket_quantity")
        
        with col2:
            bracket_price = st.number_input("Entry Price:", min_value=0.0, value=0.0, step=0.05, key="bracket_price")
            stop_loss_price = st.number_input("Stop Loss:", min_value=0.0, value=0.0, step=0.05)
        
        with col3:
            target_price = st.number_input("Target:", min_value=0.0, value=0.0, step=0.05)
            bracket_exchange = st.selectbox("Exchange:", ["nse_cm", "nse_fo"], key="bracket_exchange")
        
        if st.button("Place Bracket Order", key="place_bracket_btn"):
            self.place_bracket_order(bracket_symbol, bracket_side, bracket_quantity, 
                                   bracket_price, stop_loss_price, target_price, bracket_exchange)
        
        st.markdown("---")
        
        # Order book
        st.subheader("📋 Order Book")
        self.display_orders()
    
    def place_order(self, symbol, side, quantity, order_type, price, trigger_price, product, exchange):
        """Place a new order"""
        try:
            if not st.session_state.order_manager:
                st.error("Order manager not initialized!")
                return
            
            order = Order(
                symbol=symbol.upper(),
                side=OrderSide.BUY if side == "BUY" else OrderSide.SELL,
                quantity=quantity,
                order_type=OrderType[order_type],
                price=price,
                trigger_price=trigger_price,
                exchange=exchange,
                product=product
            )
            
            with st.spinner("Placing order..."):
                order_id = st.session_state.order_manager.place_order(order)
                
                if order_id:
                    st.success(f"✅ Order placed successfully! Order ID: {order_id}")
                else:
                    st.error("❌ Failed to place order!")
                    
        except Exception as e:
            st.error(f"❌ Error placing order: {e}")
    
    def place_bracket_order(self, symbol, side, quantity, price, stop_loss_price, target_price, exchange):
        """Place a bracket order"""
        try:
            if not st.session_state.order_manager:
                st.error("Order manager not initialized!")
                return
            
            with st.spinner("Placing bracket order..."):
                order_id = st.session_state.order_manager.place_bracket_order(
                    symbol=symbol.upper(),
                    side=OrderSide.BUY if side == "BUY" else OrderSide.SELL,
                    quantity=quantity,
                    price=price,
                    stop_loss_price=stop_loss_price,
                    target_price=target_price,
                    exchange=exchange
                )
                
                if order_id:
                    st.success(f"✅ Bracket order placed successfully! Order ID: {order_id}")
                else:
                    st.error("❌ Failed to place bracket order!")
                    
        except Exception as e:
            st.error(f"❌ Error placing bracket order: {e}")
    
    def display_orders(self):
        """Display order book"""
        if st.session_state.order_manager:
            orders = st.session_state.order_manager.get_orders()
            
            if orders:
                order_data = []
                for order in orders:
                    order_data.append({
                        'Order ID': order.order_id or 'Pending',
                        'Symbol': order.symbol,
                        'Side': order.side.value,
                        'Quantity': order.quantity,
                        'Type': order.order_type.value,
                        'Price': f"₹{order.price:.2f}" if order.price > 0 else 'Market',
                        'Status': order.status.value,
                        'Time': order.timestamp.strftime('%H:%M:%S') if order.timestamp else 'N/A'
                    })
                
                df = pd.DataFrame(order_data)
                st.dataframe(df, use_container_width=True)
            else:
                st.info("No orders found")
    
    def render_portfolio(self):
        """Render portfolio view"""
        st.header("💼 Portfolio")

        if st.session_state.order_manager:
            positions = st.session_state.order_manager.get_positions()

            if positions:
                position_data = []
                total_pnl = 0

                for position in positions:
                    pnl_color = "🟢" if position.pnl >= 0 else "🔴"
                    position_data.append({
                        'Symbol': position.symbol,
                        'Quantity': position.quantity,
                        'Avg Price': f"₹{position.average_price:.2f}",
                        'Current Price': f"₹{position.current_price:.2f}",
                        'P&L': f"{pnl_color} ₹{position.pnl:.2f}",
                        'Exchange': position.exchange
                    })
                    total_pnl += position.pnl

                # Summary
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("Total Positions", len(positions))
                with col2:
                    st.metric("Total P&L", f"₹{total_pnl:.2f}")
                with col3:
                    pnl_color = "green" if total_pnl >= 0 else "red"
                    st.markdown(f'<p style="color:{pnl_color}">{"📈" if total_pnl >= 0 else "📉"} {"Profit" if total_pnl >= 0 else "Loss"}</p>',
                               unsafe_allow_html=True)

                # Positions table
                df = pd.DataFrame(position_data)
                st.dataframe(df, use_container_width=True)

                # P&L chart
                if len(position_data) > 0:
                    fig = px.bar(
                        x=[pos['Symbol'] for pos in position_data],
                        y=[float(pos['P&L'].split('₹')[1]) for pos in position_data],
                        title="Position-wise P&L",
                        color=[float(pos['P&L'].split('₹')[1]) for pos in position_data],
                        color_continuous_scale=['red', 'green']
                    )
                    st.plotly_chart(fig, use_container_width=True)
            else:
                st.info("No positions found")

    def render_scheduler(self):
        """Render trade scheduler interface"""
        st.header("⏰ Trade Scheduler")

        # Scheduler status
        if st.session_state.trade_scheduler:
            status = st.session_state.trade_scheduler.get_scheduler_status()

            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("Total Tasks", status['total_tasks'])
            with col2:
                st.metric("Pending", status['pending_tasks'])
            with col3:
                st.metric("Completed", status['completed_tasks'])
            with col4:
                st.metric("Failed", status['failed_tasks'])

        st.markdown("---")

        # Schedule new order
        st.subheader("📅 Schedule Order")

        col1, col2 = st.columns(2)

        with col1:
            sched_symbol = st.text_input("Symbol:", key="sched_symbol")
            sched_side = st.selectbox("Side:", ["BUY", "SELL"], key="sched_side")
            sched_quantity = st.number_input("Quantity:", min_value=1, value=1, key="sched_quantity")
            sched_price = st.number_input("Price:", min_value=0.0, value=0.0, step=0.05, key="sched_price")

        with col2:
            sched_date = st.date_input("Date:", key="sched_date")
            sched_time = st.time_input("Time:", key="sched_time")
            sched_type = st.selectbox("Order Type:", ["MARKET", "LIMIT"], key="sched_type")
            task_name = st.text_input("Task Name:", placeholder="Optional", key="task_name")

        if st.button("Schedule Order", key="schedule_order_btn"):
            self.schedule_order(sched_symbol, sched_side, sched_quantity, sched_price,
                              sched_date, sched_time, sched_type, task_name)

        st.markdown("---")

        # Scheduled tasks
        st.subheader("📋 Scheduled Tasks")

        if st.session_state.trade_scheduler:
            tasks = st.session_state.trade_scheduler.get_tasks()

            if tasks:
                task_data = []
                for task in tasks:
                    task_data.append({
                        'Task ID': task.task_id,
                        'Name': task.name,
                        'Type': task.task_type.value,
                        'Status': task.status.value,
                        'Next Run': task.next_run.strftime('%Y-%m-%d %H:%M:%S') if task.next_run else 'N/A',
                        'Run Count': task.run_count,
                        'Enabled': '✅' if task.enabled else '❌'
                    })

                df = pd.DataFrame(task_data)
                st.dataframe(df, use_container_width=True)

                # Task management
                selected_task = st.selectbox("Select task to manage:",
                                           [f"{task.task_id} - {task.name}" for task in tasks])

                if selected_task:
                    task_id = selected_task.split(' - ')[0]

                    col1, col2, col3 = st.columns(3)

                    with col1:
                        if st.button("Cancel Task", key="cancel_task_btn"):
                            if st.session_state.trade_scheduler.cancel_task(task_id):
                                st.success("Task cancelled!")
                                st.experimental_rerun()

                    with col2:
                        if st.button("Enable Task", key="enable_task_btn"):
                            if st.session_state.trade_scheduler.enable_task(task_id):
                                st.success("Task enabled!")
                                st.experimental_rerun()

                    with col3:
                        if st.button("Disable Task", key="disable_task_btn"):
                            if st.session_state.trade_scheduler.disable_task(task_id):
                                st.success("Task disabled!")
                                st.experimental_rerun()
            else:
                st.info("No scheduled tasks found")

    def schedule_order(self, symbol, side, quantity, price, date, time, order_type, task_name):
        """Schedule a new order"""
        try:
            if not st.session_state.trade_scheduler:
                st.error("Scheduler not initialized!")
                return

            # Combine date and time
            schedule_datetime = datetime.combine(date, time)

            # Create order
            order = Order(
                symbol=symbol.upper(),
                side=OrderSide.BUY if side == "BUY" else OrderSide.SELL,
                quantity=quantity,
                order_type=OrderType.MARKET if order_type == "MARKET" else OrderType.LIMIT,
                price=price if order_type == "LIMIT" else 0.0
            )

            # Schedule the order
            task_id = st.session_state.trade_scheduler.schedule_order(
                order=order,
                schedule_time=schedule_datetime,
                task_name=task_name or f"Scheduled {side} {symbol}"
            )

            st.success(f"✅ Order scheduled successfully! Task ID: {task_id}")

        except Exception as e:
            st.error(f"❌ Error scheduling order: {e}")

    def render_settings(self):
        """Render settings interface"""
        st.header("⚙️ Settings")

        # API Configuration
        st.subheader("🔐 API Configuration")

        config_manager = get_config()

        with st.expander("View Current Configuration"):
            st.info("For security, credentials are not displayed. Edit config.ini file directly.")

            trading_settings = config_manager.get_trading_settings()

            col1, col2 = st.columns(2)

            with col1:
                st.write("**Trading Settings:**")
                st.write(f"Default Quantity: {trading_settings['default_quantity']}")
                st.write(f"Stop Loss %: {trading_settings['stop_loss_percentage']}")
                st.write(f"Target Profit %: {trading_settings['target_profit_percentage']}")

            with col2:
                st.write("**Risk Management:**")
                st.write(f"Max Daily Loss: ₹{trading_settings['max_daily_loss']}")
                st.write(f"Max Position Size: ₹{trading_settings['max_position_size']}")

        st.markdown("---")

        # Risk Management Settings
        st.subheader("⚠️ Risk Management")

        if st.session_state.order_manager:
            col1, col2 = st.columns(2)

            with col1:
                new_max_loss = st.number_input("Max Daily Loss (₹):",
                                             value=st.session_state.order_manager.max_daily_loss,
                                             min_value=0.0)

                new_max_position = st.number_input("Max Position Size (₹):",
                                                 value=st.session_state.order_manager.max_position_size,
                                                 min_value=0.0)

            with col2:
                if st.button("Update Risk Settings", key="update_risk_btn"):
                    st.session_state.order_manager.max_daily_loss = new_max_loss
                    st.session_state.order_manager.max_position_size = new_max_position
                    st.success("✅ Risk settings updated!")

        st.markdown("---")

        # System Settings
        st.subheader("🖥️ System Settings")

        col1, col2 = st.columns(2)

        with col1:
            auto_refresh = st.checkbox("Auto Refresh Data", value=True)
            refresh_interval = st.slider("Refresh Interval (seconds):", 1, 10, 2)

        with col2:
            log_level = st.selectbox("Log Level:", ["DEBUG", "INFO", "WARNING", "ERROR"])
            enable_notifications = st.checkbox("Enable Notifications", value=True)

        if st.button("Save System Settings", key="save_system_btn"):
            st.success("✅ System settings saved!")

        st.markdown("---")

        # Data Management
        st.subheader("💾 Data Management")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("Clear Cache", key="clear_cache_btn"):
                # Clear cached data
                st.success("✅ Cache cleared!")

        with col2:
            if st.button("Export Data", key="export_data_btn"):
                # Export functionality
                st.info("Export functionality coming soon...")

        with col3:
            if st.button("Reset App", key="reset_app_btn"):
                # Reset application state
                for key in list(st.session_state.keys()):
                    del st.session_state[key]
                st.success("✅ Application reset! Please refresh the page.")

    def render_logs(self):
        """Render logs interface"""
        st.header("📝 Logs & Monitoring")

        # System Status
        st.subheader("🖥️ System Status")

        col1, col2, col3, col4 = st.columns(4)

        with col1:
            if st.session_state.core_engine:
                status = st.session_state.core_engine.get_connection_status()
                st.metric("API Connection", "✅ Connected" if status['is_connected'] else "❌ Disconnected")

        with col2:
            if st.session_state.data_handler:
                status = st.session_state.data_handler.get_data_status()
                st.metric("WebSocket", "✅ Active" if status['websocket_running'] else "❌ Inactive")

        with col3:
            if st.session_state.order_manager:
                status = st.session_state.order_manager.get_order_status()
                st.metric("Order Monitor", "✅ Active" if status['monitoring_active'] else "❌ Inactive")

        with col4:
            if st.session_state.trade_scheduler:
                status = st.session_state.trade_scheduler.get_scheduler_status()
                st.metric("Scheduler", "✅ Running" if status['running'] else "❌ Stopped")

        st.markdown("---")

        # Recent Activity
        st.subheader("📊 Recent Activity")

        # Process queues and display recent messages
        recent_messages = []

        # Check data queue
        try:
            while not st.session_state.data_queue.empty():
                msg = st.session_state.data_queue.get_nowait()
                recent_messages.append(f"[DATA] {msg.get('type', 'Unknown')}: {msg.get('timestamp', 'N/A')}")
        except:
            pass

        # Check order queue
        try:
            while not st.session_state.order_queue.empty():
                msg = st.session_state.order_queue.get_nowait()
                recent_messages.append(f"[ORDER] {msg.get('type', 'Unknown')}: {msg.get('timestamp', 'N/A')}")
        except:
            pass

        # Check scheduler queue
        try:
            while not st.session_state.scheduler_queue.empty():
                msg = st.session_state.scheduler_queue.get_nowait()
                recent_messages.append(f"[SCHEDULER] {msg.get('type', 'Unknown')}: {msg.get('timestamp', 'N/A')}")
        except:
            pass

        if recent_messages:
            for msg in recent_messages[-10:]:  # Show last 10 messages
                st.text(msg)
        else:
            st.info("No recent activity")

        st.markdown("---")

        # Performance Metrics
        st.subheader("📈 Performance Metrics")

        if st.session_state.order_manager:
            orders = st.session_state.order_manager.get_orders()

            if orders:
                # Order statistics
                total_orders = len(orders)
                completed_orders = len([o for o in orders if o.status.value == 'complete'])
                success_rate = (completed_orders / total_orders * 100) if total_orders > 0 else 0

                col1, col2, col3 = st.columns(3)

                with col1:
                    st.metric("Total Orders", total_orders)

                with col2:
                    st.metric("Completed Orders", completed_orders)

                with col3:
                    st.metric("Success Rate", f"{success_rate:.1f}%")

                # Daily P&L trend (placeholder)
                dates = pd.date_range(start=datetime.now() - timedelta(days=7), end=datetime.now(), freq='D')
                pnl_data = [0] * len(dates)  # Placeholder data

                fig = go.Figure()
                fig.add_trace(go.Scatter(x=dates, y=pnl_data, mode='lines+markers', name='Daily P&L'))
                fig.update_layout(title="Daily P&L Trend", xaxis_title="Date", yaxis_title="P&L (₹)")
                st.plotly_chart(fig, use_container_width=True)
    
    def run(self):
        """Run the main application"""
        self.render_header()
        page = self.render_sidebar()

        # Route to appropriate page
        if page == "Dashboard":
            self.render_dashboard()
        elif page == "Trading":
            self.render_trading_interface()
        elif page == "Portfolio":
            self.render_portfolio()
        elif page == "Scheduler":
            self.render_scheduler()
        elif page == "Settings":
            self.render_settings()
        elif page == "Logs":
            self.render_logs()

def main():
    """Main application entry point"""
    try:
        app = TradingBotApp()
        app.run()
    except Exception as e:
        st.error(f"❌ Application error: {e}")
        logging.error(f"Application error: {e}")

if __name__ == "__main__":
    main()
