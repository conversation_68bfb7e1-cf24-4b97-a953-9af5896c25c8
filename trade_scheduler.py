"""
Trade Scheduler for Kotak Neo Trading Bot
Manages scheduled and recurring trading tasks
"""

import logging
import threading
import queue
from datetime import datetime, timedelta, time
from typing import Dict, Any, List, Optional, Callable
from enum import Enum
from dataclasses import dataclass, field
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.date import DateTrigger
from apscheduler.triggers.interval import IntervalTrigger
from order_manager import Order, OrderType, OrderSide

class ScheduleType(Enum):
    """Types of scheduled tasks"""
    ONE_TIME = "one_time"
    RECURRING = "recurring"
    MARKET_OPEN = "market_open"
    MARKET_CLOSE = "market_close"
    INTERVAL = "interval"

class TaskStatus(Enum):
    """Task execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class ScheduledTask:
    """Scheduled task data structure"""
    task_id: str
    name: str
    task_type: ScheduleType
    function: Callable
    args: tuple = field(default_factory=tuple)
    kwargs: dict = field(default_factory=dict)
    schedule_time: datetime = None
    cron_expression: str = None
    interval_seconds: int = None
    status: TaskStatus = TaskStatus.PENDING
    created_at: datetime = field(default_factory=datetime.now)
    last_run: datetime = None
    next_run: datetime = None
    run_count: int = 0
    max_runs: int = None
    enabled: bool = True
    description: str = ""

class TradeScheduler:
    """Manages scheduled trading tasks and automation"""
    
    def __init__(self, order_manager, data_handler, scheduler_queue: queue.Queue):
        """
        Initialize the trade scheduler
        
        Args:
            order_manager: OrderManager instance for trade execution
            data_handler: DataHandler instance for market data
            scheduler_queue: Queue for scheduler communications
        """
        self.order_manager = order_manager
        self.data_handler = data_handler
        self.scheduler_queue = scheduler_queue
        self.scheduler = BackgroundScheduler()
        self.tasks = {}  # task_id -> ScheduledTask
        self.task_counter = 0
        
        # Market timing (can be configured)
        self.market_open_time = time(9, 15)  # 9:15 AM
        self.market_close_time = time(15, 30)  # 3:30 PM
        
        # Start the scheduler
        self.scheduler.start()
        logging.info("Trade scheduler initialized and started")
    
    def generate_task_id(self) -> str:
        """Generate unique task ID"""
        self.task_counter += 1
        return f"task_{self.task_counter}_{int(datetime.now().timestamp())}"
    
    def schedule_order(self, order: Order, schedule_time: datetime, 
                      task_name: str = None) -> str:
        """
        Schedule an order to be placed at a specific time
        
        Args:
            order: Order object to be placed
            schedule_time: When to place the order
            task_name: Optional task name
            
        Returns:
            Task ID
        """
        task_id = self.generate_task_id()
        task_name = task_name or f"Scheduled Order - {order.symbol}"
        
        def place_scheduled_order():
            try:
                logging.info(f"Executing scheduled order: {order.symbol}")
                order_id = self.order_manager.place_order(order)
                
                if order_id:
                    self.scheduler_queue.put({
                        'type': 'scheduled_order_executed',
                        'task_id': task_id,
                        'order_id': order_id,
                        'order': order,
                        'timestamp': datetime.now()
                    })
                    self.tasks[task_id].status = TaskStatus.COMPLETED
                else:
                    self.tasks[task_id].status = TaskStatus.FAILED
                    
            except Exception as e:
                logging.error(f"Error executing scheduled order: {e}")
                self.tasks[task_id].status = TaskStatus.FAILED
        
        task = ScheduledTask(
            task_id=task_id,
            name=task_name,
            task_type=ScheduleType.ONE_TIME,
            function=place_scheduled_order,
            schedule_time=schedule_time,
            description=f"Place {order.side.value} order for {order.quantity} {order.symbol}"
        )
        
        # Schedule the task
        self.scheduler.add_job(
            func=self._execute_task,
            args=[task_id],
            trigger=DateTrigger(run_date=schedule_time),
            id=task_id,
            name=task_name
        )
        
        self.tasks[task_id] = task
        task.next_run = schedule_time
        
        logging.info(f"Order scheduled for {schedule_time}: {task_name}")
        return task_id
    
    def schedule_bracket_order(self, symbol: str, side: OrderSide, quantity: int,
                             price: float, stop_loss_price: float, target_price: float,
                             schedule_time: datetime, exchange: str = "nse_cm",
                             task_name: str = None) -> str:
        """
        Schedule a bracket order
        
        Args:
            symbol: Trading symbol
            side: Order side
            quantity: Order quantity
            price: Order price
            stop_loss_price: Stop loss price
            target_price: Target price
            schedule_time: When to place the order
            exchange: Exchange segment
            task_name: Optional task name
            
        Returns:
            Task ID
        """
        task_id = self.generate_task_id()
        task_name = task_name or f"Scheduled Bracket Order - {symbol}"
        
        def place_bracket_order():
            try:
                logging.info(f"Executing scheduled bracket order: {symbol}")
                order_id = self.order_manager.place_bracket_order(
                    symbol=symbol,
                    side=side,
                    quantity=quantity,
                    price=price,
                    stop_loss_price=stop_loss_price,
                    target_price=target_price,
                    exchange=exchange
                )
                
                if order_id:
                    self.scheduler_queue.put({
                        'type': 'scheduled_bracket_order_executed',
                        'task_id': task_id,
                        'order_id': order_id,
                        'symbol': symbol,
                        'timestamp': datetime.now()
                    })
                    self.tasks[task_id].status = TaskStatus.COMPLETED
                else:
                    self.tasks[task_id].status = TaskStatus.FAILED
                    
            except Exception as e:
                logging.error(f"Error executing scheduled bracket order: {e}")
                self.tasks[task_id].status = TaskStatus.FAILED
        
        task = ScheduledTask(
            task_id=task_id,
            name=task_name,
            task_type=ScheduleType.ONE_TIME,
            function=place_bracket_order,
            schedule_time=schedule_time,
            description=f"Bracket order: {side.value} {quantity} {symbol} @ {price}"
        )
        
        # Schedule the task
        self.scheduler.add_job(
            func=self._execute_task,
            args=[task_id],
            trigger=DateTrigger(run_date=schedule_time),
            id=task_id,
            name=task_name
        )
        
        self.tasks[task_id] = task
        task.next_run = schedule_time
        
        logging.info(f"Bracket order scheduled for {schedule_time}: {task_name}")
        return task_id
    
    def schedule_recurring_task(self, function: Callable, cron_expression: str,
                              task_name: str, args: tuple = (), kwargs: dict = None,
                              max_runs: int = None) -> str:
        """
        Schedule a recurring task using cron expression
        
        Args:
            function: Function to execute
            cron_expression: Cron expression for scheduling
            task_name: Task name
            args: Function arguments
            kwargs: Function keyword arguments
            max_runs: Maximum number of runs (None for unlimited)
            
        Returns:
            Task ID
        """
        task_id = self.generate_task_id()
        kwargs = kwargs or {}
        
        task = ScheduledTask(
            task_id=task_id,
            name=task_name,
            task_type=ScheduleType.RECURRING,
            function=function,
            args=args,
            kwargs=kwargs,
            cron_expression=cron_expression,
            max_runs=max_runs,
            description=f"Recurring task: {cron_expression}"
        )
        
        # Schedule the task
        self.scheduler.add_job(
            func=self._execute_task,
            args=[task_id],
            trigger=CronTrigger.from_crontab(cron_expression),
            id=task_id,
            name=task_name
        )
        
        self.tasks[task_id] = task
        
        logging.info(f"Recurring task scheduled: {task_name} ({cron_expression})")
        return task_id
    
    def schedule_interval_task(self, function: Callable, interval_seconds: int,
                             task_name: str, args: tuple = (), kwargs: dict = None,
                             max_runs: int = None) -> str:
        """
        Schedule a task to run at regular intervals
        
        Args:
            function: Function to execute
            interval_seconds: Interval in seconds
            task_name: Task name
            args: Function arguments
            kwargs: Function keyword arguments
            max_runs: Maximum number of runs
            
        Returns:
            Task ID
        """
        task_id = self.generate_task_id()
        kwargs = kwargs or {}
        
        task = ScheduledTask(
            task_id=task_id,
            name=task_name,
            task_type=ScheduleType.INTERVAL,
            function=function,
            args=args,
            kwargs=kwargs,
            interval_seconds=interval_seconds,
            max_runs=max_runs,
            description=f"Interval task: every {interval_seconds} seconds"
        )
        
        # Schedule the task
        self.scheduler.add_job(
            func=self._execute_task,
            args=[task_id],
            trigger=IntervalTrigger(seconds=interval_seconds),
            id=task_id,
            name=task_name
        )
        
        self.tasks[task_id] = task
        
        logging.info(f"Interval task scheduled: {task_name} (every {interval_seconds}s)")
        return task_id
    
    def schedule_market_open_task(self, function: Callable, task_name: str,
                                args: tuple = (), kwargs: dict = None) -> str:
        """
        Schedule a task to run at market open
        
        Args:
            function: Function to execute
            task_name: Task name
            args: Function arguments
            kwargs: Function keyword arguments
            
        Returns:
            Task ID
        """
        # Schedule for market open time on weekdays
        cron_expression = f"{self.market_open_time.minute} {self.market_open_time.hour} * * MON-FRI"
        
        task_id = self.schedule_recurring_task(
            function=function,
            cron_expression=cron_expression,
            task_name=task_name,
            args=args,
            kwargs=kwargs or {}
        )
        
        self.tasks[task_id].task_type = ScheduleType.MARKET_OPEN
        self.tasks[task_id].description = "Market open task"
        
        return task_id
    
    def schedule_market_close_task(self, function: Callable, task_name: str,
                                 args: tuple = (), kwargs: dict = None) -> str:
        """
        Schedule a task to run at market close
        
        Args:
            function: Function to execute
            task_name: Task name
            args: Function arguments
            kwargs: Function keyword arguments
            
        Returns:
            Task ID
        """
        # Schedule for market close time on weekdays
        cron_expression = f"{self.market_close_time.minute} {self.market_close_time.hour} * * MON-FRI"
        
        task_id = self.schedule_recurring_task(
            function=function,
            cron_expression=cron_expression,
            task_name=task_name,
            args=args,
            kwargs=kwargs or {}
        )
        
        self.tasks[task_id].task_type = ScheduleType.MARKET_CLOSE
        self.tasks[task_id].description = "Market close task"
        
        return task_id
    
    def _execute_task(self, task_id: str) -> None:
        """Execute a scheduled task"""
        if task_id not in self.tasks:
            logging.error(f"Task not found: {task_id}")
            return
        
        task = self.tasks[task_id]
        
        # Check if task is enabled
        if not task.enabled:
            logging.info(f"Task disabled, skipping: {task.name}")
            return
        
        # Check max runs limit
        if task.max_runs and task.run_count >= task.max_runs:
            logging.info(f"Task reached max runs limit: {task.name}")
            self.cancel_task(task_id)
            return
        
        try:
            task.status = TaskStatus.RUNNING
            task.last_run = datetime.now()
            task.run_count += 1
            
            logging.info(f"Executing task: {task.name}")
            
            # Execute the task function
            if task.args and task.kwargs:
                task.function(*task.args, **task.kwargs)
            elif task.args:
                task.function(*task.args)
            elif task.kwargs:
                task.function(**task.kwargs)
            else:
                task.function()
            
            # Update status based on task type
            if task.task_type == ScheduleType.ONE_TIME:
                task.status = TaskStatus.COMPLETED
            else:
                task.status = TaskStatus.PENDING
            
            # Notify about task execution
            self.scheduler_queue.put({
                'type': 'task_executed',
                'task_id': task_id,
                'task_name': task.name,
                'run_count': task.run_count,
                'timestamp': datetime.now()
            })
            
            logging.info(f"Task executed successfully: {task.name}")
            
        except Exception as e:
            task.status = TaskStatus.FAILED
            logging.error(f"Error executing task {task.name}: {e}")
            
            self.scheduler_queue.put({
                'type': 'task_failed',
                'task_id': task_id,
                'task_name': task.name,
                'error': str(e),
                'timestamp': datetime.now()
            })
    
    def cancel_task(self, task_id: str) -> bool:
        """
        Cancel a scheduled task
        
        Args:
            task_id: Task ID to cancel
            
        Returns:
            True if cancelled successfully, False otherwise
        """
        try:
            if task_id in self.tasks:
                self.scheduler.remove_job(task_id)
                self.tasks[task_id].status = TaskStatus.CANCELLED
                
                logging.info(f"Task cancelled: {self.tasks[task_id].name}")
                
                self.scheduler_queue.put({
                    'type': 'task_cancelled',
                    'task_id': task_id,
                    'task_name': self.tasks[task_id].name,
                    'timestamp': datetime.now()
                })
                
                return True
            else:
                logging.error(f"Task not found: {task_id}")
                return False
                
        except Exception as e:
            logging.error(f"Error cancelling task {task_id}: {e}")
            return False
    
    def enable_task(self, task_id: str) -> bool:
        """Enable a task"""
        if task_id in self.tasks:
            self.tasks[task_id].enabled = True
            logging.info(f"Task enabled: {self.tasks[task_id].name}")
            return True
        return False
    
    def disable_task(self, task_id: str) -> bool:
        """Disable a task"""
        if task_id in self.tasks:
            self.tasks[task_id].enabled = False
            logging.info(f"Task disabled: {self.tasks[task_id].name}")
            return True
        return False
    
    def get_tasks(self, status: TaskStatus = None) -> List[ScheduledTask]:
        """
        Get tasks, optionally filtered by status
        
        Args:
            status: Task status to filter by
            
        Returns:
            List of tasks
        """
        if status:
            return [task for task in self.tasks.values() if task.status == status]
        return list(self.tasks.values())
    
    def get_task(self, task_id: str) -> Optional[ScheduledTask]:
        """Get a specific task by ID"""
        return self.tasks.get(task_id)
    
    def get_next_runs(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get upcoming task runs
        
        Args:
            limit: Maximum number of runs to return
            
        Returns:
            List of upcoming runs
        """
        upcoming = []
        
        for job in self.scheduler.get_jobs():
            if job.next_run_time:
                task = self.tasks.get(job.id)
                upcoming.append({
                    'task_id': job.id,
                    'task_name': task.name if task else job.name,
                    'next_run': job.next_run_time,
                    'task_type': task.task_type.value if task else 'unknown'
                })
        
        # Sort by next run time
        upcoming.sort(key=lambda x: x['next_run'])
        
        return upcoming[:limit]
    
    def shutdown(self) -> None:
        """Shutdown the scheduler"""
        try:
            self.scheduler.shutdown(wait=True)
            logging.info("Trade scheduler shutdown completed")
        except Exception as e:
            logging.error(f"Error shutting down scheduler: {e}")
    
    def get_scheduler_status(self) -> Dict[str, Any]:
        """
        Get scheduler status information
        
        Returns:
            Dictionary with status information
        """
        return {
            'running': self.scheduler.running,
            'total_tasks': len(self.tasks),
            'pending_tasks': len([t for t in self.tasks.values() if t.status == TaskStatus.PENDING]),
            'completed_tasks': len([t for t in self.tasks.values() if t.status == TaskStatus.COMPLETED]),
            'failed_tasks': len([t for t in self.tasks.values() if t.status == TaskStatus.FAILED]),
            'active_jobs': len(self.scheduler.get_jobs()),
            'next_run': min([job.next_run_time for job in self.scheduler.get_jobs() 
                           if job.next_run_time], default=None)
        }
