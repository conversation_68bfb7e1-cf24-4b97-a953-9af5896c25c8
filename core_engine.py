"""
Core Engine for Kotak Neo Trading Bot
Handles API authentication, session management, and scrip master operations
"""

import logging
import pandas as pd
import os
import time
from datetime import date, datetime
from functools import wraps
from typing import Dict, Any, Optional, List
from neo_api_client import NeoAPI, BaseUrl
from config_manager import get_config

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def handle_session_expiry(func):
    """Decorator to handle API session expiry and re-authenticate"""
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        try:
            return func(self, *args, **kwargs)
        except Exception as e:
            error_msg = str(e).lower()
            # Check for session expiry indicators
            if any(indicator in error_msg for indicator in ['invalid session', '403', 'unauthorized', 'session expired']):
                logging.warning("Session expired or invalid. Re-authenticating...")
                if self.connect():
                    # Retry the original function call
                    return func(self, *args, **kwargs)
                else:
                    logging.error("Failed to re-authenticate")
                    raise
            else:
                logging.error(f"An unexpected error occurred in {func.__name__}: {e}")
                raise
    return wrapper

class CoreEngine:
    """Core engine for Kotak Neo API integration"""
    
    def __init__(self):
        """Initialize the core engine"""
        self.config_manager = get_config()
        self.client = None
        self.scrip_master_df = None
        self.session_token = None
        self.is_connected = False
        self.last_heartbeat = None
        
        # Validate credentials on initialization
        if not self.config_manager.validate_credentials():
            raise ValueError("Invalid or missing credentials in config.ini")
    
    def connect(self) -> bool:
        """
        Establish connection and authenticate with Kotak Neo API
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            credentials = self.config_manager.get_credentials()
            
            # Get base URL for the UCC
            base_url = BaseUrl(ucc=credentials['ucc']).get_base_url()
            logging.info(f"Using base URL: {base_url}")
            
            # Initialize the Neo API client
            self.client = NeoAPI(
                consumer_key=credentials['consumer_key'],
                consumer_secret=credentials['consumer_secret'],
                environment='prod',  # Use 'uat' for testing, 'prod' for live trading
                base_url=base_url
            )
            
            # Step 1: TOTP Login
            login_response = self.client.totp_login(
                mobile_number=credentials['mobile_number'],
                ucc=credentials['ucc'],
                totp=credentials['password']  # In V2, password is used as TOTP
            )
            logging.info("TOTP Login successful")
            
            # Step 2: Validate with MPIN to get session token
            validate_response = self.client.totp_validate(mpin=credentials['mpin'])
            logging.info("Session validated with MPIN")
            
            self.session_token = validate_response.get('sessionToken')
            self.is_connected = True
            self.last_heartbeat = datetime.now()
            
            # Load scrip master after successful connection
            self.load_and_cache_scrip_master()
            
            logging.info("Connection to Kotak Neo API established successfully")
            return True
            
        except Exception as e:
            logging.error(f"Failed to connect to Kotak Neo API: {e}")
            self.client = None
            self.is_connected = False
            return False
    
    def disconnect(self) -> None:
        """Disconnect from the API"""
        try:
            if self.client:
                # Logout if possible
                self.client.logout()
                logging.info("Logged out from Kotak Neo API")
        except Exception as e:
            logging.warning(f"Error during logout: {e}")
        finally:
            self.client = None
            self.is_connected = False
            self.session_token = None
    
    def get_client(self) -> Optional[NeoAPI]:
        """
        Get the authenticated client instance
        
        Returns:
            NeoAPI client if connected, None otherwise
        """
        if not self.is_connected or not self.client:
            if not self.connect():
                return None
        return self.client
    
    def load_and_cache_scrip_master(self) -> None:
        """
        Load the scrip master file with caching mechanism
        Downloads new version if cached one is outdated or missing
        """
        today_str = date.today().strftime('%Y-%m-%d')
        filepath = f"scrip_master_{today_str}.csv"
        
        # Check if today's cached file exists
        if os.path.exists(filepath):
            logging.info(f"Loading scrip master from cached file: {filepath}")
            try:
                self.scrip_master_df = pd.read_csv(filepath)
                return
            except Exception as e:
                logging.warning(f"Failed to load cached scrip master: {e}")
        
        # Download new scrip master file
        logging.info("Downloading new scrip master file...")
        client = self.get_client()
        if not client:
            logging.error("Cannot download scrip master: No active client connection")
            return
        
        try:
            # Download for relevant exchanges
            exchanges = ['nse_cm', 'nse_fo', 'mcx_fo']
            all_data = []
            
            for exchange in exchanges:
                try:
                    logging.info(f"Downloading scrip master for {exchange}")
                    client.scrip_master(exchange_segment=exchange)
                    
                    # The SDK saves it to a file named like 'scripmaster-nse_cm.csv'
                    source_file = f"scripmaster-{exchange}.csv"
                    if os.path.exists(source_file):
                        df = pd.read_csv(source_file)
                        df['exchange'] = exchange  # Add exchange column for identification
                        all_data.append(df)
                        
                        # Clean up the original file
                        os.remove(source_file)
                        
                except Exception as e:
                    logging.warning(f"Failed to download scrip master for {exchange}: {e}")
            
            if all_data:
                # Combine all exchange data
                self.scrip_master_df = pd.concat(all_data, ignore_index=True)
                
                # Save consolidated file
                self.scrip_master_df.to_csv(filepath, index=False)
                logging.info(f"Scrip master downloaded and cached as {filepath}")
                
                # Clean up old cached files
                self._cleanup_old_scrip_files(today_str)
            else:
                logging.error("Failed to download scrip master for any exchange")
                
        except Exception as e:
            logging.error(f"Failed to download scrip master: {e}")
    
    def _cleanup_old_scrip_files(self, current_date: str) -> None:
        """Clean up old scrip master files"""
        try:
            for filename in os.listdir('.'):
                if filename.startswith('scrip_master_') and not filename.endswith(f'{current_date}.csv'):
                    os.remove(filename)
                    logging.info(f"Removed old scrip master file: {filename}")
        except Exception as e:
            logging.warning(f"Error cleaning up old scrip files: {e}")
    
    @handle_session_expiry
    def get_instrument_details(self, symbol: str, exchange: str = 'nse_cm') -> Optional[Dict[str, Any]]:
        """
        Search the scrip master for a given trading symbol
        
        Args:
            symbol: Trading symbol to search for
            exchange: Exchange segment
            
        Returns:
            Dictionary with instrument details or None if not found
        """
        if self.scrip_master_df is None:
            self.load_and_cache_scrip_master()
        
        if self.scrip_master_df is None:
            logging.error("Scrip master not available")
            return None
        
        try:
            # Filter by exchange and symbol
            # Column names may vary, common ones are 'pTrdSymbol', 'pSymbol', 'symbol'
            symbol_columns = ['pTrdSymbol', 'pSymbol', 'symbol', 'trading_symbol']
            
            for col in symbol_columns:
                if col in self.scrip_master_df.columns:
                    mask = (self.scrip_master_df[col].str.upper() == symbol.upper()) & \
                           (self.scrip_master_df['exchange'] == exchange)
                    instrument = self.scrip_master_df[mask]
                    
                    if not instrument.empty:
                        return instrument.iloc[0].to_dict()
            
            logging.warning(f"Instrument not found: {symbol} on {exchange}")
            return None
            
        except Exception as e:
            logging.error(f"Error searching for instrument {symbol}: {e}")
            return None
    
    @handle_session_expiry
    def search_instruments(self, search_term: str, exchange: str = 'nse_cm', limit: int = 10) -> List[Dict[str, Any]]:
        """
        Search for instruments matching the search term
        
        Args:
            search_term: Term to search for
            exchange: Exchange segment
            limit: Maximum number of results
            
        Returns:
            List of matching instruments
        """
        if self.scrip_master_df is None:
            self.load_and_cache_scrip_master()
        
        if self.scrip_master_df is None:
            return []
        
        try:
            # Filter by exchange
            exchange_data = self.scrip_master_df[self.scrip_master_df['exchange'] == exchange]
            
            # Search in symbol columns
            symbol_columns = ['pTrdSymbol', 'pSymbol', 'symbol', 'trading_symbol', 'pDesc', 'description']
            
            results = []
            for col in symbol_columns:
                if col in exchange_data.columns:
                    mask = exchange_data[col].str.contains(search_term, case=False, na=False)
                    matches = exchange_data[mask].head(limit)
                    results.extend(matches.to_dict('records'))
            
            # Remove duplicates and limit results
            seen = set()
            unique_results = []
            for item in results:
                identifier = item.get('pTrdSymbol', item.get('symbol', ''))
                if identifier not in seen:
                    seen.add(identifier)
                    unique_results.append(item)
                    if len(unique_results) >= limit:
                        break
            
            return unique_results
            
        except Exception as e:
            logging.error(f"Error searching instruments: {e}")
            return []
    
    def is_market_open(self) -> bool:
        """
        Check if the market is currently open
        This is a simplified check - in production, you'd want more sophisticated logic
        
        Returns:
            True if market is likely open, False otherwise
        """
        now = datetime.now()
        
        # Basic check: Monday to Friday, 9:15 AM to 3:30 PM
        if now.weekday() >= 5:  # Saturday = 5, Sunday = 6
            return False
        
        market_open = now.replace(hour=9, minute=15, second=0, microsecond=0)
        market_close = now.replace(hour=15, minute=30, second=0, microsecond=0)
        
        return market_open <= now <= market_close
    
    def get_connection_status(self) -> Dict[str, Any]:
        """
        Get current connection status
        
        Returns:
            Dictionary with connection status information
        """
        return {
            'is_connected': self.is_connected,
            'session_token': self.session_token is not None,
            'last_heartbeat': self.last_heartbeat,
            'scrip_master_loaded': self.scrip_master_df is not None,
            'market_open': self.is_market_open()
        }
