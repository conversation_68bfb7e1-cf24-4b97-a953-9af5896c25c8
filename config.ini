[DEFAULT]
# Kotak Neo API Credentials
# Get these from your Kotak Securities Neo Trade API registration
CONSUMER_KEY = YOUR_KEY_HERE
CONSUMER_SECRET = YOUR_SECRET_HERE
UCC = YOUR_UNIQUE_CLIENT_CODE

# Login Credentials
MO<PERSON>LE_NUMBER = YOUR_<PERSON><PERSON><PERSON>LE_NUMBER
PASSWORD = YOUR_PASSWORD
MPIN = YOUR_MPIN

# Trading Settings
DEFAULT_QUANTITY = 1
STOP_LOSS_PERCENTAGE = 2.0
TARGET_PROFIT_PERCENTAGE = 3.0
MAX_DAILY_LOSS = 5000
MAX_POSITION_SIZE = 10000

# Application Settings
LOG_LEVEL = INFO
REFRESH_INTERVAL = 1
AUTO_RECONNECT = true
CACHE_DURATION_HOURS = 24
