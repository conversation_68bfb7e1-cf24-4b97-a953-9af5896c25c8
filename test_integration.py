#!/usr/bin/env python3
"""
Integration Test Suite for Kotak Neo Trading Bot
Tests all modules and their interactions
"""

import unittest
import logging
import queue
import time
import threading
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock

# Import our modules
from config_manager import ConfigManager, get_config
from core_engine import CoreEngine
from data_handler import DataHandler
from order_manager import OrderManager, Order, OrderType, OrderSide
from trade_scheduler import TradeScheduler

# Configure logging for tests
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class TestConfigManager(unittest.TestCase):
    """Test configuration management"""
    
    def test_config_loading(self):
        """Test configuration file loading"""
        try:
            config = get_config()
            self.assertIsInstance(config, ConfigManager)
            
            # Test basic configuration access
            credentials = config.get_credentials()
            self.assertIn('consumer_key', credentials)
            self.assertIn('consumer_secret', credentials)
            
            trading_settings = config.get_trading_settings()
            self.assertIn('default_quantity', trading_settings)
            
            print("✅ Configuration manager test passed")
            
        except Exception as e:
            print(f"❌ Configuration manager test failed: {e}")
            raise

class TestCoreEngine(unittest.TestCase):
    """Test core engine functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.core_engine = CoreEngine()
    
    def test_initialization(self):
        """Test core engine initialization"""
        self.assertIsNotNone(self.core_engine.config_manager)
        self.assertIsNone(self.core_engine.client)
        self.assertFalse(self.core_engine.is_connected)
        print("✅ Core engine initialization test passed")
    
    def test_connection_status(self):
        """Test connection status reporting"""
        status = self.core_engine.get_connection_status()
        self.assertIn('is_connected', status)
        self.assertIn('market_open', status)
        print("✅ Core engine status test passed")
    
    @patch('core_engine.NeoAPI')
    def test_mock_connection(self, mock_neo_api):
        """Test connection with mocked API"""
        # Mock the API client
        mock_client = Mock()
        mock_client.totp_login.return_value = {'stat': 'Ok'}
        mock_client.totp_validate.return_value = {'stat': 'Ok', 'sessionToken': 'test_token'}
        mock_neo_api.return_value = mock_client
        
        # Test connection
        success = self.core_engine.connect()
        self.assertTrue(success)
        print("✅ Core engine mock connection test passed")

class TestDataHandler(unittest.TestCase):
    """Test data handler functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.core_engine = Mock()
        self.data_queue = queue.Queue()
        self.data_handler = DataHandler(self.core_engine, self.data_queue)
    
    def test_initialization(self):
        """Test data handler initialization"""
        self.assertEqual(self.data_handler.core_engine, self.core_engine)
        self.assertEqual(self.data_handler.data_queue, self.data_queue)
        self.assertEqual(len(self.data_handler.subscribed_tokens), 0)
        print("✅ Data handler initialization test passed")
    
    def test_callback_registration(self):
        """Test callback registration"""
        def test_callback(data):
            pass
        
        self.data_handler.register_callback('market_data', test_callback)
        self.assertIn('market_data', self.data_handler.callbacks)
        self.assertEqual(len(self.data_handler.callbacks['market_data']), 1)
        print("✅ Data handler callback test passed")
    
    def test_message_processing(self):
        """Test WebSocket message processing"""
        # Mock message
        test_message = [{'tk': '12345', 'lp': 100.50, 'c': 2.5, 'cp': 2.55}]
        
        # Process message
        self.data_handler._on_message(test_message)
        
        # Check if data was stored
        self.assertIn('12345', self.data_handler.price_data)
        self.assertEqual(self.data_handler.price_data['12345']['ltp'], 100.50)
        
        # Check if message was queued
        self.assertFalse(self.data_queue.empty())
        
        print("✅ Data handler message processing test passed")

class TestOrderManager(unittest.TestCase):
    """Test order manager functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.core_engine = Mock()
        self.data_handler = Mock()
        self.order_queue = queue.Queue()
        self.order_manager = OrderManager(self.core_engine, self.data_handler, self.order_queue)
    
    def test_initialization(self):
        """Test order manager initialization"""
        self.assertEqual(self.order_manager.core_engine, self.core_engine)
        self.assertEqual(self.order_manager.data_handler, self.data_handler)
        self.assertEqual(len(self.order_manager.orders), 0)
        print("✅ Order manager initialization test passed")
    
    def test_order_validation(self):
        """Test order validation"""
        # Create test order
        order = Order(
            symbol="RELIANCE",
            side=OrderSide.BUY,
            quantity=1,
            order_type=OrderType.MARKET
        )
        
        # Test validation
        is_valid = self.order_manager._validate_order(order)
        self.assertTrue(is_valid)
        print("✅ Order validation test passed")
    
    def test_order_creation(self):
        """Test order object creation"""
        order = Order(
            symbol="TCS",
            side=OrderSide.SELL,
            quantity=5,
            order_type=OrderType.LIMIT,
            price=3500.0
        )
        
        self.assertEqual(order.symbol, "TCS")
        self.assertEqual(order.side, OrderSide.SELL)
        self.assertEqual(order.quantity, 5)
        self.assertEqual(order.price, 3500.0)
        print("✅ Order creation test passed")

class TestTradeScheduler(unittest.TestCase):
    """Test trade scheduler functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.order_manager = Mock()
        self.data_handler = Mock()
        self.scheduler_queue = queue.Queue()
        self.trade_scheduler = TradeScheduler(
            self.order_manager, 
            self.data_handler, 
            self.scheduler_queue
        )
    
    def test_initialization(self):
        """Test scheduler initialization"""
        self.assertIsNotNone(self.trade_scheduler.scheduler)
        self.assertTrue(self.trade_scheduler.scheduler.running)
        self.assertEqual(len(self.trade_scheduler.tasks), 0)
        print("✅ Trade scheduler initialization test passed")
    
    def test_task_id_generation(self):
        """Test unique task ID generation"""
        task_id1 = self.trade_scheduler.generate_task_id()
        task_id2 = self.trade_scheduler.generate_task_id()
        
        self.assertNotEqual(task_id1, task_id2)
        self.assertTrue(task_id1.startswith('task_'))
        print("✅ Task ID generation test passed")
    
    def test_scheduler_status(self):
        """Test scheduler status reporting"""
        status = self.trade_scheduler.get_scheduler_status()
        
        self.assertIn('running', status)
        self.assertIn('total_tasks', status)
        self.assertTrue(status['running'])
        print("✅ Scheduler status test passed")
    
    def tearDown(self):
        """Clean up after tests"""
        self.trade_scheduler.shutdown()

class TestIntegration(unittest.TestCase):
    """Test module integration"""
    
    def setUp(self):
        """Set up integrated test environment"""
        # Create queues for inter-module communication
        self.data_queue = queue.Queue()
        self.order_queue = queue.Queue()
        self.scheduler_queue = queue.Queue()
        
        # Create mock core engine
        self.core_engine = Mock()
        self.core_engine.get_connection_status.return_value = {
            'is_connected': True,
            'market_open': True,
            'scrip_master_loaded': True
        }
        
        # Initialize modules
        self.data_handler = DataHandler(self.core_engine, self.data_queue)
        self.order_manager = OrderManager(self.core_engine, self.data_handler, self.order_queue)
        self.trade_scheduler = TradeScheduler(
            self.order_manager, 
            self.data_handler, 
            self.scheduler_queue
        )
    
    def test_queue_communication(self):
        """Test inter-module queue communication"""
        # Test data queue
        test_data = {'type': 'test', 'data': 'test_message'}
        self.data_queue.put(test_data)
        
        retrieved_data = self.data_queue.get()
        self.assertEqual(retrieved_data['type'], 'test')
        
        # Test order queue
        test_order_msg = {'type': 'order_placed', 'order_id': 'test_123'}
        self.order_queue.put(test_order_msg)
        
        retrieved_order = self.order_queue.get()
        self.assertEqual(retrieved_order['type'], 'order_placed')
        
        print("✅ Queue communication test passed")
    
    def test_module_interaction(self):
        """Test interaction between modules"""
        # Test data handler and order manager interaction
        self.data_handler.price_data['12345'] = {
            'token': '12345',
            'ltp': 100.0,
            'change': 1.0,
            'change_percent': 1.0,
            'volume': 1000,
            'timestamp': datetime.now()
        }
        
        # Mock get_latest_price to return test data
        self.data_handler.get_latest_price = Mock(return_value=self.data_handler.price_data['12345'])
        
        # Test order manager can access price data
        price_data = self.data_handler.get_latest_price('TEST')
        self.assertIsNotNone(price_data)
        self.assertEqual(price_data['ltp'], 100.0)
        
        print("✅ Module interaction test passed")
    
    def test_error_handling(self):
        """Test error handling across modules"""
        # Test data handler error handling
        try:
            self.data_handler._on_error("Test error")
            # Should not raise exception
            print("✅ Data handler error handling test passed")
        except Exception as e:
            self.fail(f"Data handler error handling failed: {e}")
        
        # Test order manager error handling
        try:
            invalid_order = Order(
                symbol="",  # Invalid symbol
                side=OrderSide.BUY,
                quantity=0,  # Invalid quantity
                order_type=OrderType.MARKET
            )
            # Should handle gracefully
            print("✅ Order manager error handling test passed")
        except Exception as e:
            self.fail(f"Order manager error handling failed: {e}")
    
    def tearDown(self):
        """Clean up after integration tests"""
        self.order_manager.stop_monitoring()
        self.trade_scheduler.shutdown()

def run_comprehensive_test():
    """Run comprehensive test suite"""
    print("🧪 Starting Kotak Neo Trading Bot Integration Tests")
    print("=" * 60)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_suite.addTest(unittest.makeSuite(TestConfigManager))
    test_suite.addTest(unittest.makeSuite(TestCoreEngine))
    test_suite.addTest(unittest.makeSuite(TestDataHandler))
    test_suite.addTest(unittest.makeSuite(TestOrderManager))
    test_suite.addTest(unittest.makeSuite(TestTradeScheduler))
    test_suite.addTest(unittest.makeSuite(TestIntegration))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print("🧪 Test Summary")
    print("=" * 60)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ Failures:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("\n❌ Errors:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    if result.wasSuccessful():
        print("\n✅ All tests passed! The trading bot is ready for use.")
        return True
    else:
        print("\n❌ Some tests failed. Please review and fix issues before using the bot.")
        return False

if __name__ == "__main__":
    success = run_comprehensive_test()
    exit(0 if success else 1)
