#!/usr/bin/env python3
"""
Kotak Neo Trading Bot Startup Script
Handles environment setup and application launch
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('trading_bot.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        logging.error("Python 3.8 or higher is required!")
        return False
    logging.info(f"Python version: {sys.version}")
    return True

def check_config_file():
    """Check if config.ini exists and is properly configured"""
    config_path = Path("config.ini")
    
    if not config_path.exists():
        logging.error("config.ini file not found!")
        logging.info("Please create config.ini with your Kotak Neo API credentials")
        return False
    
    # Read config and check for placeholder values
    with open(config_path, 'r') as f:
        content = f.read()
        
    if "YOUR_KEY_HERE" in content or "YOUR_SECRET_HERE" in content:
        logging.error("config.ini contains placeholder values!")
        logging.info("Please update config.ini with your actual Kotak Neo API credentials")
        return False
    
    logging.info("Configuration file validated")
    return True

def install_dependencies():
    """Install required dependencies"""
    try:
        logging.info("Installing dependencies...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        logging.info("Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logging.error(f"Failed to install dependencies: {e}")
        return False

def check_dependencies():
    """Check if all required packages are installed"""
    required_packages = [
        'streamlit',
        'pandas',
        'plotly',
        'yfinance',
        'apscheduler',
        'sqlalchemy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logging.warning(f"Missing packages: {missing_packages}")
        return False
    
    logging.info("All dependencies are installed")
    return True

def create_directories():
    """Create necessary directories"""
    directories = ['logs', 'data', 'exports']
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        logging.info(f"Created directory: {directory}")

def run_streamlit_app():
    """Run the Streamlit application"""
    try:
        logging.info("Starting Kotak Neo Trading Bot...")
        logging.info("Open your browser and go to: http://localhost:8501")
        
        # Run Streamlit app
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "app.py",
            "--server.port", "8501",
            "--server.address", "localhost",
            "--browser.gatherUsageStats", "false"
        ])
        
    except KeyboardInterrupt:
        logging.info("Application stopped by user")
    except Exception as e:
        logging.error(f"Error running application: {e}")

def main():
    """Main startup function"""
    print("=" * 60)
    print("🚀 Kotak Neo Trading Bot Startup")
    print("=" * 60)
    
    setup_logging()
    
    # Pre-flight checks
    if not check_python_version():
        sys.exit(1)
    
    if not check_config_file():
        sys.exit(1)
    
    # Check and install dependencies
    if not check_dependencies():
        logging.info("Installing missing dependencies...")
        if not install_dependencies():
            sys.exit(1)
    
    # Create necessary directories
    create_directories()
    
    # Display important information
    print("\n📋 Important Information:")
    print("1. Make sure you have updated config.ini with your Kotak Neo API credentials")
    print("2. Ensure you have active internet connection")
    print("3. The application will open in your default web browser")
    print("4. Use Ctrl+C to stop the application")
    print("\n⚠️  Disclaimer:")
    print("This is a trading bot that can place real trades.")
    print("Use at your own risk and start with small amounts.")
    print("Always test in UAT environment first.")
    
    # Ask for confirmation
    try:
        response = input("\n✅ Do you want to continue? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("Startup cancelled by user")
            sys.exit(0)
    except KeyboardInterrupt:
        print("\nStartup cancelled by user")
        sys.exit(0)
    
    # Run the application
    run_streamlit_app()

if __name__ == "__main__":
    main()
