"""
Data Handler for Kotak Neo Trading Bot
Manages real-time WebSocket data feeds and historical data
"""

import logging
import threading
import time
import queue
import json
import pandas as pd
import yfinance as yf
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
from core_engine import CoreEngine

class DataHandler:
    """Handles real-time and historical market data"""
    
    def __init__(self, core_engine: CoreEngine, data_queue: queue.Queue):
        """
        Initialize the data handler
        
        Args:
            core_engine: CoreEngine instance for API access
            data_queue: Queue for inter-thread communication
        """
        self.core_engine = core_engine
        self.data_queue = data_queue
        self.subscribed_tokens = set()
        self.ws_thread = None
        self.stop_ws = threading.Event()
        self.last_msg_time = None
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        self.reconnect_delay = 5  # seconds
        self.price_data = {}  # Store latest price data
        self.callbacks = {}  # Store callback functions for different data types
        
        # Setup WebSocket callbacks
        self._setup_callbacks()
    
    def _setup_callbacks(self) -> None:
        """Setup WebSocket event callbacks"""
        client = self.core_engine.get_client()
        if client:
            client.on_message = self._on_message
            client.on_error = self._on_error
            client.on_close = self._on_close
            client.on_open = self._on_open
    
    def _on_message(self, message: Any) -> None:
        """Handle incoming WebSocket messages"""
        try:
            self.last_msg_time = time.time()
            
            # Parse the message (format varies by API)
            if isinstance(message, str):
                data = json.loads(message)
            else:
                data = message
            
            # Store latest price data
            if isinstance(data, list):
                for item in data:
                    if isinstance(item, dict) and 'tk' in item:
                        token = item['tk']
                        self.price_data[token] = {
                            'token': token,
                            'ltp': item.get('lp', 0),
                            'change': item.get('c', 0),
                            'change_percent': item.get('cp', 0),
                            'volume': item.get('v', 0),
                            'timestamp': datetime.now()
                        }
            
            # Put data in queue for UI updates
            self.data_queue.put({
                'type': 'market_data',
                'data': data,
                'timestamp': datetime.now()
            })
            
            # Execute registered callbacks
            for callback in self.callbacks.get('market_data', []):
                try:
                    callback(data)
                except Exception as e:
                    logging.error(f"Error in market data callback: {e}")
            
            logging.debug(f"WebSocket message received: {data}")
            
        except Exception as e:
            logging.error(f"Error processing WebSocket message: {e}")
    
    def _on_error(self, error: Any) -> None:
        """Handle WebSocket errors"""
        logging.error(f"WebSocket error: {error}")
        self.data_queue.put({
            'type': 'error',
            'data': str(error),
            'timestamp': datetime.now()
        })
    
    def _on_open(self, message: Any) -> None:
        """Handle WebSocket connection open"""
        logging.info(f"WebSocket connection opened: {message}")
        self.reconnect_attempts = 0
        
        # Resubscribe to tokens if connection is re-established
        if self.subscribed_tokens:
            self._resubscribe_tokens()
        
        self.data_queue.put({
            'type': 'connection',
            'data': 'connected',
            'timestamp': datetime.now()
        })
    
    def _on_close(self, message: Any) -> None:
        """Handle WebSocket connection close"""
        logging.warning(f"WebSocket connection closed: {message}")
        self.data_queue.put({
            'type': 'connection',
            'data': 'disconnected',
            'timestamp': datetime.now()
        })
        
        # Attempt to reconnect if not intentionally stopped
        if not self.stop_ws.is_set():
            self._schedule_reconnect()
    
    def _schedule_reconnect(self) -> None:
        """Schedule WebSocket reconnection with exponential backoff"""
        if self.reconnect_attempts < self.max_reconnect_attempts:
            delay = self.reconnect_delay * (2 ** self.reconnect_attempts)
            self.reconnect_attempts += 1
            
            logging.info(f"Scheduling reconnect attempt {self.reconnect_attempts} in {delay} seconds")
            
            def reconnect():
                time.sleep(delay)
                if not self.stop_ws.is_set():
                    self.start_websocket()
            
            threading.Thread(target=reconnect, daemon=True).start()
        else:
            logging.error("Max reconnection attempts reached")
    
    def _resubscribe_tokens(self) -> None:
        """Resubscribe to all previously subscribed tokens"""
        try:
            client = self.core_engine.get_client()
            if client and self.subscribed_tokens:
                client.subscribe(list(self.subscribed_tokens))
                logging.info(f"Resubscribed to {len(self.subscribed_tokens)} tokens")
        except Exception as e:
            logging.error(f"Error resubscribing to tokens: {e}")
    
    def start_websocket(self) -> bool:
        """
        Start the WebSocket connection in a background thread
        
        Returns:
            True if started successfully, False otherwise
        """
        try:
            if self.ws_thread and self.ws_thread.is_alive():
                logging.warning("WebSocket thread already running")
                return True
            
            client = self.core_engine.get_client()
            if not client:
                logging.error("No active client connection for WebSocket")
                return False
            
            self.stop_ws.clear()
            self.ws_thread = threading.Thread(target=self._run_websocket, daemon=True)
            self.ws_thread.start()
            
            logging.info("WebSocket thread started")
            return True
            
        except Exception as e:
            logging.error(f"Failed to start WebSocket: {e}")
            return False
    
    def _run_websocket(self) -> None:
        """Run the WebSocket connection"""
        try:
            client = self.core_engine.get_client()
            if client:
                # Start the WebSocket connection
                client.websocket_login()
                logging.info("WebSocket login successful")
                
                # Keep the connection alive
                while not self.stop_ws.is_set():
                    time.sleep(1)
                    
                    # Check for stale data (no messages for 30 seconds)
                    if self.last_msg_time and (time.time() - self.last_msg_time) > 30:
                        logging.warning("WebSocket data appears stale, reconnecting...")
                        break
                        
        except Exception as e:
            logging.error(f"WebSocket connection error: {e}")
        finally:
            logging.info("WebSocket thread ended")
    
    def stop_websocket(self) -> None:
        """Stop the WebSocket connection"""
        try:
            self.stop_ws.set()
            
            if self.ws_thread and self.ws_thread.is_alive():
                self.ws_thread.join(timeout=5)
                
            logging.info("WebSocket stopped")
            
        except Exception as e:
            logging.error(f"Error stopping WebSocket: {e}")
    
    def subscribe_to_symbols(self, symbols: List[str], exchange: str = 'nse_cm') -> bool:
        """
        Subscribe to real-time data for given symbols
        
        Args:
            symbols: List of trading symbols
            exchange: Exchange segment
            
        Returns:
            True if subscription successful, False otherwise
        """
        try:
            client = self.core_engine.get_client()
            if not client:
                logging.error("No active client connection")
                return False
            
            # Get instrument tokens for symbols
            tokens = []
            for symbol in symbols:
                instrument = self.core_engine.get_instrument_details(symbol, exchange)
                if instrument and 'pToken' in instrument:
                    tokens.append(instrument['pToken'])
                    self.subscribed_tokens.add(instrument['pToken'])
                else:
                    logging.warning(f"Could not find token for symbol: {symbol}")
            
            if tokens:
                # Subscribe to the tokens
                client.subscribe(tokens)
                logging.info(f"Subscribed to {len(tokens)} instruments")
                return True
            else:
                logging.error("No valid tokens found for subscription")
                return False
                
        except Exception as e:
            logging.error(f"Error subscribing to symbols: {e}")
            return False
    
    def unsubscribe_from_symbols(self, symbols: List[str], exchange: str = 'nse_cm') -> bool:
        """
        Unsubscribe from real-time data for given symbols
        
        Args:
            symbols: List of trading symbols
            exchange: Exchange segment
            
        Returns:
            True if unsubscription successful, False otherwise
        """
        try:
            client = self.core_engine.get_client()
            if not client:
                return False
            
            # Get instrument tokens for symbols
            tokens = []
            for symbol in symbols:
                instrument = self.core_engine.get_instrument_details(symbol, exchange)
                if instrument and 'pToken' in instrument:
                    tokens.append(instrument['pToken'])
                    self.subscribed_tokens.discard(instrument['pToken'])
            
            if tokens:
                # Unsubscribe from the tokens
                client.unsubscribe(tokens)
                logging.info(f"Unsubscribed from {len(tokens)} instruments")
                return True
            else:
                return False
                
        except Exception as e:
            logging.error(f"Error unsubscribing from symbols: {e}")
            return False
    
    def get_historical_data(self, symbol: str, period: str = '1d', interval: str = '1m', 
                          exchange: str = 'nse_cm') -> Optional[pd.DataFrame]:
        """
        Get historical data for a symbol
        
        Args:
            symbol: Trading symbol
            period: Data period (1d, 5d, 1mo, 3mo, 6mo, 1y, 2y, 5y, 10y, ytd, max)
            interval: Data interval (1m, 2m, 5m, 15m, 30m, 60m, 90m, 1h, 1d, 5d, 1wk, 1mo, 3mo)
            exchange: Exchange segment
            
        Returns:
            DataFrame with historical data or None if failed
        """
        try:
            # For NSE symbols, we can use yfinance as a fallback
            if exchange == 'nse_cm':
                # Convert symbol to Yahoo Finance format
                yf_symbol = f"{symbol}.NS"
                
                ticker = yf.Ticker(yf_symbol)
                data = ticker.history(period=period, interval=interval)
                
                if not data.empty:
                    # Reset index to make datetime a column
                    data.reset_index(inplace=True)
                    data.columns = [col.lower() for col in data.columns]
                    return data
            
            # TODO: Implement Kotak Neo API historical data when available
            logging.warning(f"Historical data not available for {symbol} on {exchange}")
            return None
            
        except Exception as e:
            logging.error(f"Error fetching historical data for {symbol}: {e}")
            return None
    
    def get_latest_price(self, symbol: str, exchange: str = 'nse_cm') -> Optional[Dict[str, Any]]:
        """
        Get latest price data for a symbol
        
        Args:
            symbol: Trading symbol
            exchange: Exchange segment
            
        Returns:
            Dictionary with latest price data or None
        """
        try:
            instrument = self.core_engine.get_instrument_details(symbol, exchange)
            if not instrument or 'pToken' not in instrument:
                return None
            
            token = instrument['pToken']
            return self.price_data.get(token)
            
        except Exception as e:
            logging.error(f"Error getting latest price for {symbol}: {e}")
            return None
    
    def register_callback(self, data_type: str, callback: Callable) -> None:
        """
        Register a callback function for specific data types
        
        Args:
            data_type: Type of data ('market_data', 'error', 'connection')
            callback: Callback function to execute
        """
        if data_type not in self.callbacks:
            self.callbacks[data_type] = []
        self.callbacks[data_type].append(callback)
    
    def get_subscribed_symbols(self) -> List[str]:
        """
        Get list of currently subscribed symbols
        
        Returns:
            List of subscribed symbol names
        """
        symbols = []
        for token in self.subscribed_tokens:
            # Find symbol name from token (reverse lookup in scrip master)
            if self.core_engine.scrip_master_df is not None:
                instrument = self.core_engine.scrip_master_df[
                    self.core_engine.scrip_master_df['pToken'] == token
                ]
                if not instrument.empty:
                    symbol_col = 'pTrdSymbol' if 'pTrdSymbol' in instrument.columns else 'symbol'
                    symbols.append(instrument.iloc[0][symbol_col])
        return symbols
    
    def get_data_status(self) -> Dict[str, Any]:
        """
        Get current data handler status
        
        Returns:
            Dictionary with status information
        """
        return {
            'websocket_running': self.ws_thread and self.ws_thread.is_alive(),
            'subscribed_tokens': len(self.subscribed_tokens),
            'last_message_time': self.last_msg_time,
            'reconnect_attempts': self.reconnect_attempts,
            'price_data_count': len(self.price_data)
        }
