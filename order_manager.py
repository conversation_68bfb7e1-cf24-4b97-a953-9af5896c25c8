"""
Order Manager for Kotak Neo Trading Bot
Handles trade execution, order management, and position monitoring
"""

import logging
import threading
import time
import queue
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
from enum import Enum
from dataclasses import dataclass
from core_engine import CoreEngine, handle_session_expiry

class OrderType(Enum):
    """Order types supported by the system"""
    MARKET = "MKT"
    LIMIT = "L"
    STOP_LOSS = "SL"
    STOP_LOSS_MARKET = "SL-M"

class OrderSide(Enum):
    """Order sides"""
    BUY = "B"
    SELL = "S"

class OrderStatus(Enum):
    """Order status types"""
    PENDING = "pending"
    OPEN = "open"
    COMPLETE = "complete"
    CANCELLED = "cancelled"
    REJECTED = "rejected"

@dataclass
class Order:
    """Order data structure"""
    symbol: str
    side: OrderSide
    quantity: int
    order_type: OrderType
    price: float = 0.0
    trigger_price: float = 0.0
    exchange: str = "nse_cm"
    product: str = "CNC"  # CNC, MIS, NRML
    validity: str = "DAY"  # DAY, IOC
    order_id: str = None
    status: OrderStatus = OrderStatus.PENDING
    filled_quantity: int = 0
    average_price: float = 0.0
    timestamp: datetime = None
    message: str = ""

@dataclass
class Position:
    """Position data structure"""
    symbol: str
    quantity: int
    average_price: float
    current_price: float
    pnl: float
    exchange: str
    product: str

class OrderManager:
    """Manages order execution and position monitoring"""
    
    def __init__(self, core_engine: CoreEngine, data_handler, order_queue: queue.Queue):
        """
        Initialize the order manager
        
        Args:
            core_engine: CoreEngine instance for API access
            data_handler: DataHandler instance for price updates
            order_queue: Queue for order-related communications
        """
        self.core_engine = core_engine
        self.data_handler = data_handler
        self.order_queue = order_queue
        self.orders = {}  # order_id -> Order
        self.positions = {}  # symbol -> Position
        self.stop_loss_orders = {}  # symbol -> stop loss details
        self.target_orders = {}  # symbol -> target details
        self.monitoring_thread = None
        self.stop_monitoring = threading.Event()
        self.order_callbacks = {}  # order_id -> callback function
        
        # Risk management settings
        self.max_daily_loss = 5000
        self.max_position_size = 10000
        self.daily_pnl = 0.0
        self.trade_count = 0
        
        # Start position monitoring
        self.start_monitoring()
    
    @handle_session_expiry
    def place_order(self, order: Order) -> Optional[str]:
        """
        Place a new order
        
        Args:
            order: Order object with order details
            
        Returns:
            Order ID if successful, None otherwise
        """
        try:
            client = self.core_engine.get_client()
            if not client:
                logging.error("No active client connection")
                return None
            
            # Get instrument details
            instrument = self.core_engine.get_instrument_details(order.symbol, order.exchange)
            if not instrument:
                logging.error(f"Instrument not found: {order.symbol}")
                return None
            
            # Risk checks
            if not self._validate_order(order):
                return None
            
            # Prepare order parameters
            order_params = {
                "instrument_token": instrument.get('pToken'),
                "exchange_segment": order.exchange,
                "product": order.product,
                "price": str(order.price) if order.price > 0 else "0",
                "order_type": order.order_type.value,
                "quantity": str(order.quantity),
                "validity": order.validity,
                "trading_symbol": instrument.get('pTrdSymbol', order.symbol),
                "transaction_type": order.side.value
            }
            
            # Add trigger price for stop loss orders
            if order.order_type in [OrderType.STOP_LOSS, OrderType.STOP_LOSS_MARKET]:
                order_params["trigger_price"] = str(order.trigger_price)
            
            # Place the order
            response = client.place_order(**order_params)
            
            if response and response.get('stat') == 'Ok':
                order_id = response.get('nOrdNo')
                order.order_id = order_id
                order.status = OrderStatus.OPEN
                order.timestamp = datetime.now()
                
                # Store the order
                self.orders[order_id] = order
                
                logging.info(f"Order placed successfully: {order_id}")
                
                # Notify via queue
                self.order_queue.put({
                    'type': 'order_placed',
                    'order_id': order_id,
                    'order': order,
                    'timestamp': datetime.now()
                })
                
                return order_id
            else:
                error_msg = response.get('emsg', 'Unknown error') if response else 'No response'
                logging.error(f"Failed to place order: {error_msg}")
                order.status = OrderStatus.REJECTED
                order.message = error_msg
                return None
                
        except Exception as e:
            logging.error(f"Error placing order: {e}")
            order.status = OrderStatus.REJECTED
            order.message = str(e)
            return None
    
    @handle_session_expiry
    def modify_order(self, order_id: str, price: float = None, quantity: int = None, 
                    trigger_price: float = None) -> bool:
        """
        Modify an existing order
        
        Args:
            order_id: Order ID to modify
            price: New price (optional)
            quantity: New quantity (optional)
            trigger_price: New trigger price (optional)
            
        Returns:
            True if modification successful, False otherwise
        """
        try:
            if order_id not in self.orders:
                logging.error(f"Order not found: {order_id}")
                return False
            
            client = self.core_engine.get_client()
            if not client:
                return False
            
            order = self.orders[order_id]
            
            # Prepare modification parameters
            modify_params = {
                "order_id": order_id,
                "exchange_segment": order.exchange,
                "product": order.product,
                "validity": order.validity,
                "trading_symbol": order.symbol
            }
            
            if price is not None:
                modify_params["price"] = str(price)
                order.price = price
            
            if quantity is not None:
                modify_params["quantity"] = str(quantity)
                order.quantity = quantity
            
            if trigger_price is not None:
                modify_params["trigger_price"] = str(trigger_price)
                order.trigger_price = trigger_price
            
            # Modify the order
            response = client.modify_order(**modify_params)
            
            if response and response.get('stat') == 'Ok':
                logging.info(f"Order modified successfully: {order_id}")
                
                self.order_queue.put({
                    'type': 'order_modified',
                    'order_id': order_id,
                    'order': order,
                    'timestamp': datetime.now()
                })
                
                return True
            else:
                error_msg = response.get('emsg', 'Unknown error') if response else 'No response'
                logging.error(f"Failed to modify order: {error_msg}")
                return False
                
        except Exception as e:
            logging.error(f"Error modifying order: {e}")
            return False
    
    @handle_session_expiry
    def cancel_order(self, order_id: str) -> bool:
        """
        Cancel an existing order
        
        Args:
            order_id: Order ID to cancel
            
        Returns:
            True if cancellation successful, False otherwise
        """
        try:
            if order_id not in self.orders:
                logging.error(f"Order not found: {order_id}")
                return False
            
            client = self.core_engine.get_client()
            if not client:
                return False
            
            order = self.orders[order_id]
            
            # Cancel the order
            response = client.cancel_order(order_id=order_id)
            
            if response and response.get('stat') == 'Ok':
                order.status = OrderStatus.CANCELLED
                logging.info(f"Order cancelled successfully: {order_id}")
                
                self.order_queue.put({
                    'type': 'order_cancelled',
                    'order_id': order_id,
                    'order': order,
                    'timestamp': datetime.now()
                })
                
                return True
            else:
                error_msg = response.get('emsg', 'Unknown error') if response else 'No response'
                logging.error(f"Failed to cancel order: {error_msg}")
                return False
                
        except Exception as e:
            logging.error(f"Error cancelling order: {e}")
            return False
    
    def place_bracket_order(self, symbol: str, side: OrderSide, quantity: int, 
                          price: float, stop_loss_price: float, target_price: float,
                          exchange: str = "nse_cm") -> Optional[str]:
        """
        Place a bracket order with stop loss and target
        
        Args:
            symbol: Trading symbol
            side: Order side (BUY/SELL)
            quantity: Order quantity
            price: Order price
            stop_loss_price: Stop loss price
            target_price: Target price
            exchange: Exchange segment
            
        Returns:
            Main order ID if successful, None otherwise
        """
        try:
            # Place main order
            main_order = Order(
                symbol=symbol,
                side=side,
                quantity=quantity,
                order_type=OrderType.LIMIT,
                price=price,
                exchange=exchange
            )
            
            main_order_id = self.place_order(main_order)
            if not main_order_id:
                return None
            
            # Store stop loss and target details for monitoring
            self.stop_loss_orders[symbol] = {
                'price': stop_loss_price,
                'quantity': quantity,
                'side': OrderSide.SELL if side == OrderSide.BUY else OrderSide.BUY,
                'main_order_id': main_order_id
            }
            
            self.target_orders[symbol] = {
                'price': target_price,
                'quantity': quantity,
                'side': OrderSide.SELL if side == OrderSide.BUY else OrderSide.BUY,
                'main_order_id': main_order_id
            }
            
            logging.info(f"Bracket order placed: {main_order_id}")
            return main_order_id
            
        except Exception as e:
            logging.error(f"Error placing bracket order: {e}")
            return None
    
    def _validate_order(self, order: Order) -> bool:
        """
        Validate order against risk management rules
        
        Args:
            order: Order to validate
            
        Returns:
            True if order is valid, False otherwise
        """
        try:
            # Check daily loss limit
            if self.daily_pnl <= -self.max_daily_loss:
                logging.error(f"Daily loss limit exceeded: {self.daily_pnl}")
                return False
            
            # Check position size
            order_value = order.quantity * order.price
            if order_value > self.max_position_size:
                logging.error(f"Position size too large: {order_value}")
                return False
            
            # Check if market is open
            if not self.core_engine.is_market_open():
                logging.warning("Market is closed")
                # Allow order placement but warn user
            
            return True
            
        except Exception as e:
            logging.error(f"Error validating order: {e}")
            return False
    
    def start_monitoring(self) -> None:
        """Start position and order monitoring thread"""
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            return
        
        self.stop_monitoring.clear()
        self.monitoring_thread = threading.Thread(target=self._monitor_positions, daemon=True)
        self.monitoring_thread.start()
        logging.info("Position monitoring started")
    
    def stop_monitoring(self) -> None:
        """Stop position monitoring"""
        self.stop_monitoring.set()
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        logging.info("Position monitoring stopped")
    
    def _monitor_positions(self) -> None:
        """Monitor positions for stop loss and target triggers"""
        while not self.stop_monitoring.is_set():
            try:
                # Update positions
                self._update_positions()
                
                # Check stop loss and target triggers
                self._check_stop_loss_triggers()
                self._check_target_triggers()
                
                # Update order statuses
                self._update_order_statuses()
                
                time.sleep(1)  # Check every second
                
            except Exception as e:
                logging.error(f"Error in position monitoring: {e}")
                time.sleep(5)
    
    def _update_positions(self) -> None:
        """Update current positions"""
        try:
            client = self.core_engine.get_client()
            if not client:
                return
            
            # Get positions from API
            response = client.positions()
            if response and response.get('stat') == 'Ok':
                positions_data = response.get('data', [])
                
                for pos_data in positions_data:
                    symbol = pos_data.get('tsym')
                    if symbol:
                        position = Position(
                            symbol=symbol,
                            quantity=int(pos_data.get('netqty', 0)),
                            average_price=float(pos_data.get('avgprc', 0)),
                            current_price=float(pos_data.get('lp', 0)),
                            pnl=float(pos_data.get('urmtom', 0)),
                            exchange=pos_data.get('exch', 'nse_cm'),
                            product=pos_data.get('prd', 'CNC')
                        )
                        self.positions[symbol] = position
                        
        except Exception as e:
            logging.error(f"Error updating positions: {e}")
    
    def _check_stop_loss_triggers(self) -> None:
        """Check for stop loss triggers"""
        for symbol, sl_details in list(self.stop_loss_orders.items()):
            try:
                current_price_data = self.data_handler.get_latest_price(symbol)
                if not current_price_data:
                    continue
                
                current_price = current_price_data['ltp']
                sl_price = sl_details['price']
                
                # Check if stop loss should trigger
                should_trigger = False
                if sl_details['side'] == OrderSide.SELL and current_price <= sl_price:
                    should_trigger = True
                elif sl_details['side'] == OrderSide.BUY and current_price >= sl_price:
                    should_trigger = True
                
                if should_trigger:
                    # Place stop loss order
                    sl_order = Order(
                        symbol=symbol,
                        side=sl_details['side'],
                        quantity=sl_details['quantity'],
                        order_type=OrderType.MARKET
                    )
                    
                    sl_order_id = self.place_order(sl_order)
                    if sl_order_id:
                        logging.info(f"Stop loss triggered for {symbol} at {current_price}")
                        del self.stop_loss_orders[symbol]
                        # Also remove target order
                        if symbol in self.target_orders:
                            del self.target_orders[symbol]
                            
            except Exception as e:
                logging.error(f"Error checking stop loss for {symbol}: {e}")
    
    def _check_target_triggers(self) -> None:
        """Check for target triggers"""
        for symbol, target_details in list(self.target_orders.items()):
            try:
                current_price_data = self.data_handler.get_latest_price(symbol)
                if not current_price_data:
                    continue
                
                current_price = current_price_data['ltp']
                target_price = target_details['price']
                
                # Check if target should trigger
                should_trigger = False
                if target_details['side'] == OrderSide.SELL and current_price >= target_price:
                    should_trigger = True
                elif target_details['side'] == OrderSide.BUY and current_price <= target_price:
                    should_trigger = True
                
                if should_trigger:
                    # Place target order
                    target_order = Order(
                        symbol=symbol,
                        side=target_details['side'],
                        quantity=target_details['quantity'],
                        order_type=OrderType.MARKET
                    )
                    
                    target_order_id = self.place_order(target_order)
                    if target_order_id:
                        logging.info(f"Target reached for {symbol} at {current_price}")
                        del self.target_orders[symbol]
                        # Also remove stop loss order
                        if symbol in self.stop_loss_orders:
                            del self.stop_loss_orders[symbol]
                            
            except Exception as e:
                logging.error(f"Error checking target for {symbol}: {e}")
    
    def _update_order_statuses(self) -> None:
        """Update order statuses from API"""
        try:
            client = self.core_engine.get_client()
            if not client:
                return
            
            # Get order book
            response = client.order_report()
            if response and response.get('stat') == 'Ok':
                orders_data = response.get('data', [])
                
                for order_data in orders_data:
                    order_id = order_data.get('nOrdNo')
                    if order_id in self.orders:
                        order = self.orders[order_id]
                        
                        # Update order status
                        status_map = {
                            'COMPLETE': OrderStatus.COMPLETE,
                            'OPEN': OrderStatus.OPEN,
                            'CANCELLED': OrderStatus.CANCELLED,
                            'REJECTED': OrderStatus.REJECTED
                        }
                        
                        new_status = status_map.get(order_data.get('status'), order.status)
                        if new_status != order.status:
                            order.status = new_status
                            order.filled_quantity = int(order_data.get('fillshares', 0))
                            order.average_price = float(order_data.get('avgprc', 0))
                            
                            # Notify about status change
                            self.order_queue.put({
                                'type': 'order_update',
                                'order_id': order_id,
                                'order': order,
                                'timestamp': datetime.now()
                            })
                            
        except Exception as e:
            logging.error(f"Error updating order statuses: {e}")
    
    def get_orders(self, status: OrderStatus = None) -> List[Order]:
        """
        Get orders, optionally filtered by status
        
        Args:
            status: Order status to filter by (optional)
            
        Returns:
            List of orders
        """
        if status:
            return [order for order in self.orders.values() if order.status == status]
        return list(self.orders.values())
    
    def get_positions(self) -> List[Position]:
        """
        Get current positions
        
        Returns:
            List of positions
        """
        return list(self.positions.values())
    
    def get_daily_pnl(self) -> float:
        """
        Calculate daily P&L
        
        Returns:
            Daily P&L amount
        """
        total_pnl = sum(pos.pnl for pos in self.positions.values())
        return total_pnl
    
    def get_order_status(self) -> Dict[str, Any]:
        """
        Get order manager status
        
        Returns:
            Dictionary with status information
        """
        return {
            'total_orders': len(self.orders),
            'open_orders': len([o for o in self.orders.values() if o.status == OrderStatus.OPEN]),
            'completed_orders': len([o for o in self.orders.values() if o.status == OrderStatus.COMPLETE]),
            'positions_count': len(self.positions),
            'daily_pnl': self.get_daily_pnl(),
            'monitoring_active': self.monitoring_thread and self.monitoring_thread.is_alive()
        }
