# 📈 Kotak Neo Trading Bot

A comprehensive, professional-grade automated trading bot built for the Kotak Neo API. This application provides real-time market data, automated trading capabilities, portfolio management, and advanced scheduling features through an intuitive web interface.

## 🌟 Features

### Core Functionality
- **Real-time Market Data**: WebSocket-based live price feeds
- **Automated Trading**: Place, modify, and cancel orders programmatically
- **Portfolio Management**: Track positions, P&L, and performance metrics
- **Trade Scheduling**: Schedule orders and recurring trading tasks
- **Risk Management**: Built-in stop-loss, target, and position size controls
- **Multi-Exchange Support**: NSE Cash, NSE F&O, MCX F&O

### User Interface
- **Professional Dashboard**: Real-time overview of market and portfolio
- **Trading Interface**: Easy-to-use order placement and management
- **Portfolio View**: Comprehensive position tracking and analytics
- **Scheduler**: Visual task management and automation
- **Settings**: Configurable risk parameters and system settings
- **Logs & Monitoring**: Real-time system status and activity logs

### Advanced Features
- **Bracket Orders**: Automated stop-loss and target orders
- **Session Management**: Auto-reconnection and session recovery
- **Data Caching**: Efficient scrip master and historical data caching
- **Error Handling**: Robust error recovery and logging
- **Modular Architecture**: Clean, maintainable, and extensible code

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- Kotak Neo API credentials (Consumer Key, Secret, UCC, Mobile, Password, MPIN)
- Active internet connection

### Installation

1. **Clone or download the project files**
   ```bash
   # All files should be in the same directory
   ```

2. **Configure your credentials**
   - Open `config.ini`
   - Replace placeholder values with your actual Kotak Neo API credentials:
   ```ini
   CONSUMER_KEY = your_actual_consumer_key
   CONSUMER_SECRET = your_actual_consumer_secret
   UCC = your_unique_client_code
   MOBILE_NUMBER = your_mobile_number
   PASSWORD = your_password
   MPIN = your_mpin
   ```

3. **Run the application**
   ```bash
   python run_bot.py
   ```

The startup script will:
- Check Python version compatibility
- Validate configuration
- Install required dependencies
- Create necessary directories
- Launch the web interface

### First Time Setup

1. **Get Kotak Neo API Access**
   - Register at [Kotak Securities](https://www.kotaksecurities.com/)
   - Apply for Neo Trade API access
   - Obtain your API credentials

2. **Test Environment First**
   - Change `environment='prod'` to `environment='uat'` in `core_engine.py`
   - Test all functionality before live trading

## 📁 Project Structure

```
kotak-neo-trading-bot/
├── app.py                 # Main Streamlit application
├── core_engine.py         # API connection and authentication
├── data_handler.py        # Real-time and historical data management
├── order_manager.py       # Trade execution and position monitoring
├── trade_scheduler.py     # Task scheduling and automation
├── config_manager.py      # Configuration management
├── config.ini            # Configuration file (user credentials)
├── requirements.txt       # Python dependencies
├── run_bot.py            # Startup script
└── README.md             # This file
```

## 🔧 Configuration

### API Settings
Edit `config.ini` to configure:
- **API Credentials**: Consumer key, secret, UCC, mobile, password, MPIN
- **Trading Settings**: Default quantities, stop-loss percentages
- **Risk Management**: Daily loss limits, position size limits
- **System Settings**: Logging levels, refresh intervals

### Risk Management
The bot includes several risk management features:
- **Daily Loss Limit**: Stops trading if daily loss exceeds threshold
- **Position Size Limit**: Prevents oversized positions
- **Stop-Loss Orders**: Automatic loss protection
- **Target Orders**: Automatic profit booking

## 💻 Usage

### Dashboard
- View real-time market overview
- Monitor portfolio performance
- Track system status
- Manage watchlist

### Trading
- Place market and limit orders
- Create bracket orders with stop-loss and targets
- View and manage order book
- Real-time order status updates

### Portfolio
- Track all positions
- View P&L breakdown
- Performance analytics
- Position-wise charts

### Scheduler
- Schedule orders for future execution
- Create recurring trading tasks
- Manage scheduled tasks
- View upcoming executions

### Settings
- Configure risk parameters
- Update system settings
- Manage data and cache
- Export functionality

## 🛡️ Security & Risk

### Security Features
- Credentials stored in separate config file
- No hardcoded sensitive information
- Session management and auto-reconnection
- Comprehensive error handling

### Risk Warnings
⚠️ **Important Disclaimers:**
- This bot can place real trades with real money
- Always test in UAT environment first
- Start with small amounts
- Monitor the bot actively during trading hours
- Understand all risks before using
- The developers are not responsible for any trading losses

### Best Practices
1. **Test Thoroughly**: Use UAT environment extensively
2. **Start Small**: Begin with minimal quantities
3. **Monitor Actively**: Don't leave the bot unattended
4. **Regular Backups**: Export data regularly
5. **Stay Updated**: Keep dependencies updated
6. **Risk Management**: Set appropriate limits

## 🔍 Troubleshooting

### Common Issues

**Connection Failed**
- Verify API credentials in config.ini
- Check internet connection
- Ensure Kotak Neo API service is available

**WebSocket Issues**
- Check firewall settings
- Verify network connectivity
- Restart the data feed

**Order Placement Failed**
- Verify sufficient funds
- Check market hours
- Validate order parameters

**Performance Issues**
- Close unnecessary browser tabs
- Restart the application
- Check system resources

### Logs
- Application logs are saved to `trading_bot.log`
- Use the Logs interface for real-time monitoring
- Enable DEBUG logging for detailed troubleshooting

## 🤝 Support

### Getting Help
1. Check this README for common solutions
2. Review the logs for error details
3. Verify configuration settings
4. Test in UAT environment

### Reporting Issues
When reporting issues, please include:
- Error messages from logs
- Steps to reproduce
- System information
- Configuration (without credentials)

## 📄 License

This project is provided as-is for educational and personal use. Users are responsible for compliance with all applicable laws and regulations.

## 🙏 Acknowledgments

- Built using the Kotak Neo API v2
- Powered by Streamlit for the web interface
- Uses various open-source Python libraries

---

**Happy Trading! 📈**

*Remember: Trading involves risk. Never invest more than you can afford to lose.*
